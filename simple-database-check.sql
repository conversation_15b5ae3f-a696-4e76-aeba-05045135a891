-- Simple database check to see what tables exist
-- Run this in phpMyAdmin to check your database structure

-- 1. Make sure we're using the right database
USE u618120801_SplitJob_33;

-- 2. Show all tables in the database
SELECT 'All tables in database:' as info;
SHOW TABLES;

-- 3. Check if applications table exists and show its structure
SELECT 'Applications table structure:' as info;
DESCRIBE applications;

-- 4. Check if contracts table exists and show its structure  
SELECT 'Contracts table structure:' as info;
DESCRIBE contracts;

-- 5. Check if documents table exists and show its structure
SELECT 'Documents table structure:' as info;
DESCRIBE documents;

-- 6. Show some sample data from applications table
SELECT 'Sample applications data:' as info;
SELECT * FROM applications LIMIT 5;

-- 7. Show some sample data from contracts table
SELECT 'Sample contracts data:' as info;
SELECT * FROM contracts LIMIT 5;
