-- Reset applications to proper state for offer letter release flow
USE u618120801_SplitJob_33;

-- 1. Reset applications to documents_verified state (before offer release)
UPDATE applications a
JOIN contracts c ON c.application_id = a.id
SET a.status = 'in_progress',
    c.status = 'documents_verified',
    c.offer_letter_released = FALSE,
    c.offer_letter_url = NULL,
    c.offer_letter_released_at = NULL
WHERE a.id IN (1, 2, 3)
  AND c.seeker_documents_verified = TRUE 
  AND c.referrer_documents_verified = TRUE;

-- 2. Show current state after reset
SELECT 'After Reset - Ready for Offer Letter Release:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.status as contract_status,
    c.seeker_signed,
    c.referrer_signed,
    c.seeker_documents_verified,
    c.referrer_documents_verified,
    c.offer_letter_released,
    jp.title as job_title,
    'Job Seeker can now release offer letter' as next_step
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
LEFT JOIN job_posts jp ON a.job_post_id = jp.id
WHERE a.id IN (1, 2, 3);
