import { withAuth } from "../../../lib/middleware";
import { generateContractForApplication } from "../../../lib/contractGenerator";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { applicationId } = req.body;

  if (!applicationId) {
    return res.status(400).json({ error: "Application ID is required" });
  }

  try {
    const result = await generateContractForApplication(applicationId);
    
    if (!result.success) {
      return res.status(400).json({ error: result.error, contractId: result.contractId });
    }

    res.status(200).json({
      message: "Contract generated successfully",
      contractId: result.contractId,
      success: true
    });
  } catch (error) {
    console.error("Failed to generate contract:", error);
    res.status(500).json({ error: "Failed to generate contract" });
  }
});
