import { useState, useEffect } from "react";
import { Check, X, Eye, Shield, AlertCircle, FileText, User } from "lucide-react";

export default function PeerDocumentVerification({ 
  contractId, 
  peerUserId, 
  currentUser, 
  onVerificationComplete 
}) {
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(false);
  const [peerDocuments, setPeerDocuments] = useState({});
  const [peerName, setPeerName] = useState("");
  const [error, setError] = useState(null);

  const documentLabels = {
    aadhar: "Aadhar Card",
    pan: "PAN Card",
    driving_license: "Driving License", 
    passport: "Passport",
  };

  useEffect(() => {
    fetchPeerDocuments();
  }, [peerUserId]);

  const fetchPeerDocuments = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const res = await fetch(`/api/profile/documents/${peerUserId}`);
      
      if (res.ok) {
        const data = await res.json();
        setPeerDocuments(data.documents || {});
        setPeerName(data.userName || "Unknown User");
      } else if (res.status === 403) {
        setError("You don't have permission to view these documents.");
      } else {
        const errorData = await res.json();
        setError(errorData.error || "Failed to fetch documents");
      }
    } catch (error) {
      console.error("Error fetching peer documents:", error);
      setError("Failed to load documents");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyDocuments = async () => {
    setVerifying(true);
    try {
      const res = await fetch("/api/contracts/verify-documents", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          contractId: contractId,
          action: "verify"
        }),
      });

      if (res.ok) {
        const data = await res.json();
        alert(data.message);
        if (onVerificationComplete) {
          onVerificationComplete(data);
        }
      } else {
        const error = await res.json();
        alert(error.error || "Failed to verify documents");
      }
    } catch (error) {
      alert("Failed to verify documents");
      console.error("Failed to verify documents:", error);
    } finally {
      setVerifying(false);
    }
  };

  const viewDocument = (url) => {
    window.open(url, '_blank');
  };

  const availableDocuments = Object.keys(peerDocuments).filter(type => 
    peerDocuments[type] && peerDocuments[type].url
  );

  if (loading) {
    return (
      <div className="card">
        <div className="card-content">
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-500">Loading peer documents...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-content">
          <div className="flex items-center gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="text-red-600 flex-shrink-0" size={20} />
            <div>
              <p className="font-medium text-red-800">Unable to Load Documents</p>
              <p className="text-sm text-red-600">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title flex items-center gap-2">
          <User size={20} />
          {peerName}'s Documents
        </h3>
        <p className="card-description">
          Review and verify the documents uploaded by your contract partner.
        </p>
      </div>

      <div className="card-content space-y-4">
        {availableDocuments.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto text-gray-400 mb-4" size={48} />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Documents Available</h4>
            <p className="text-gray-500 text-sm">
              {peerName} hasn't uploaded any documents to their profile yet.
            </p>
          </div>
        ) : (
          <>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-start gap-3">
                <Shield className="text-blue-600 flex-shrink-0 mt-0.5" size={16} />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Document Verification</p>
                  <p>
                    Please carefully review each document for authenticity and accuracy. 
                    Only verify if you're confident the documents are genuine.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {availableDocuments.map((type) => {
                const doc = peerDocuments[type];
                return (
                  <div key={type} className="border rounded-lg p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-lg mb-1">{documentLabels[type]}</h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <span>Uploaded: {new Date(doc.uploadedAt).toLocaleDateString()}</span>
                          {doc.verified && (
                            <div className="flex items-center gap-1 text-green-600">
                              <Shield size={14} />
                              <span>Verified</span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row gap-2">
                        <button
                          onClick={() => viewDocument(doc.url)}
                          className="btn-outline btn-sm flex items-center justify-center gap-2"
                        >
                          <Eye size={16} />
                          View Document
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="border-t pt-6 mt-6">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div>
                  <h4 className="font-medium mb-1">Verification Decision</h4>
                  <p className="text-sm text-gray-600">
                    After reviewing all documents, confirm if they appear authentic and complete.
                  </p>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                  <button
                    onClick={handleVerifyDocuments}
                    disabled={verifying}
                    className="btn-primary flex items-center justify-center gap-2 w-full sm:w-auto"
                  >
                    {verifying ? (
                      <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Check size={16} />
                    )}
                    {verifying ? "Verifying..." : "Verify Documents"}
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
