-- Test document permissions for your users
USE u618120801_SplitJob_33;

-- Check the relationship between users in contracts
SELECT 'Contract relationships:' as info;
SELECT 
    c.id as contract_id,
    a.id as app_id,
    jp.user_id as seeker_id,
    a.referrer_id,
    c.status as contract_status,
    seeker.full_name as seeker_name,
    referrer.full_name as referrer_name
FROM contracts c
JOIN applications a ON c.application_id = a.id
JOIN job_posts jp ON a.job_post_id = jp.id
JOIN users seeker ON jp.user_id = seeker.id
JOIN users referrer ON a.referrer_id = referrer.id
WHERE a.id IN (1, 2, 3);

-- Test permission query for referrer (user 3) to view seeker (user 1) documents
SELECT 'Permission test - Referrer (3) viewing Seeker (1) docs:' as info;
SELECT c.id, a.id as app_id, jp.user_id as seeker_id, a.referrer_id
FROM contracts c
JOIN applications a ON c.application_id = a.id
JOIN job_posts jp ON a.job_post_id = jp.id
WHERE (
  (jp.user_id = 3 AND a.referrer_id = 1) OR 
  (jp.user_id = 1 AND a.referrer_id = 3)
)
AND c.status IN ('signed', 'documents_pending', 'documents_verified', 'offer_released');

-- Test permission query for seeker (user 1) to view referrer (user 3) documents  
SELECT 'Permission test - Seeker (1) viewing Referrer (3) docs:' as info;
SELECT c.id, a.id as app_id, jp.user_id as seeker_id, a.referrer_id
FROM contracts c
JOIN applications a ON c.application_id = a.id
JOIN job_posts jp ON a.job_post_id = jp.id
WHERE (
  (jp.user_id = 1 AND a.referrer_id = 3) OR 
  (jp.user_id = 3 AND a.referrer_id = 1)
)
AND c.status IN ('signed', 'documents_pending', 'documents_verified', 'offer_released');
