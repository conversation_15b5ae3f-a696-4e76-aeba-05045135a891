import { query } from "../../../lib/db";

export default async function handler(req, res) {
  const { id } = req.query;

  if (req.method === "GET") {
    try {
      const jobs = await query(
        `
        SELECT 
          jp.*,
          u.full_name as user_name,
          u.email as user_email,
          u.linkedin_url,
          u.github_url,
          u.portfolio_url,
          COUNT(DISTINCT a.id) as applications_count
        FROM job_posts jp
        JOIN users u ON jp.user_id = u.id
        LEFT JOIN applications a ON jp.id = a.job_post_id
        WHERE jp.id = ? OR jp.shareable_link = ?
        GROUP BY jp.id
      `,
        [id, id]
      );

      if (jobs.length === 0) {
        return res.status(404).json({ error: "Job not found" });
      }

      const job = jobs[0];

      // If anonymous, hide user details
      if (job.visibility === "anonymous") {
        job.user_name = "Anonymous";
        job.user_email = null;
        job.linkedin_url = null;
        job.github_url = null;
        job.portfolio_url = null;
      }

      res.status(200).json({ job });
    } catch (error) {
      console.error("Failed to fetch job:", error);
      res.status(500).json({ error: "Failed to fetch job" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
}
