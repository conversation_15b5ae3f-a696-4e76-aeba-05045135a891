import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import formidable from "formidable";
import fs from "fs";
import path from "path";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Parse form data including file upload
  const form = formidable({
    uploadDir: "./public/uploads/offer-letters",
    keepExtensions: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB limit
    filter: ({ name, originalFilename, mimetype }) => {
      // Only allow PDF files
      return mimetype && mimetype.includes("pdf");
    },
  });

  // Ensure upload directory exists
  const uploadDir = "./public/uploads/offer-letters";
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  let contractId, offerLetterFile;

  try {
    const [fields, files] = await form.parse(req);
    contractId = fields.contractId?.[0];
    offerLetterFile = files.offerLetter?.[0];

    if (!contractId) {
      return res.status(400).json({ error: "Contract ID is required" });
    }

    if (!offerLetterFile) {
      return res.status(400).json({ error: "Offer letter file is required" });
    }

    // Validate file type
    if (!offerLetterFile.mimetype?.includes("pdf")) {
      return res.status(400).json({ error: "Only PDF files are allowed" });
    }
  } catch (error) {
    console.error("Error parsing form data:", error);
    return res.status(400).json({ error: "Invalid form data" });
  }

  try {
    // Get contract and application details
    const contracts = await query(
      `
      SELECT
        c.*,
        a.*,
        jp.title as job_title,
        jp.job_role,
        jp.user_id as seeker_id,
        a.company_name as job_company,
        jp.payment_type,
        jp.payment_percentage,
        jp.payment_fixed,
        seeker.full_name as seeker_name,
        seeker.email as seeker_email,
        referrer.full_name as referrer_name,
        referrer.email as referrer_email,
        c.seeker_documents_verified,
        c.referrer_documents_verified
      FROM contracts c
      JOIN applications a ON c.application_id = a.id
      JOIN job_posts jp ON a.job_post_id = jp.id
      JOIN users seeker ON jp.user_id = seeker.id
      JOIN users referrer ON a.referrer_id = referrer.id
      WHERE c.id = ? AND (
        c.status = 'documents_verified' OR
        c.status = 'offer_released' OR
        c.status = '' OR
        c.status IS NULL OR
        (c.seeker_documents_verified = TRUE AND c.referrer_documents_verified = TRUE)
      )
    `,
      [contractId]
    );

    console.log(`Offer letter upload - Contract ${contractId} query result:`, contracts.length);

    if (contracts.length === 0) {
      // Debug: Check if contract exists at all
      const debugContracts = await query(
        `SELECT c.id, c.status, c.seeker_documents_verified, c.referrer_documents_verified
         FROM contracts c WHERE c.id = ?`,
        [contractId]
      );

      console.log(`Debug - Contract ${contractId} exists:`, debugContracts);

      return res.status(404).json({
        error: "Contract not found or not ready for offer letter release",
        debug: {
          contractId,
          contractExists: debugContracts.length > 0,
          contractData: debugContracts[0] || null
        }
      });
    }

    const contract = contracts[0];

    console.log(`Offer letter upload - User verification:`, {
      requestingUserId: req.user.id,
      seekerId: contract.seeker_id,
      isJobSeeker: req.user.id === contract.seeker_id
    });

    // Verify that the requesting user is the job seeker
    if (req.user.id !== contract.seeker_id) {
      return res.status(403).json({
        error: "Only the job seeker can release the offer letter",
        debug: {
          requestingUserId: req.user.id,
          expectedSeekerUserId: contract.seeker_id
        }
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const originalName = offerLetterFile.originalFilename || "offer-letter.pdf";
    const extension = path.extname(originalName);
    const filename = `offer-letter-${contractId}-${timestamp}${extension}`;
    const newPath = path.join(uploadDir, filename);

    // Move file to final location
    fs.renameSync(offerLetterFile.filepath, newPath);

    // Generate public URL
    const offerLetterUrl = `/uploads/offer-letters/${filename}`;

    // Update contract with offer letter information
    await query(
      `UPDATE contracts 
       SET status = 'offer_released',
           offer_letter_released = TRUE,
           offer_letter_url = ?,
           offer_letter_released_at = NOW()
       WHERE id = ?`,
      [offerLetterUrl, contractId]
    );

    // Update application status to completed
    await query(
      `UPDATE applications 
       SET status = 'completed'
       WHERE id = ?`,
      [contract.application_id]
    );

    res.status(200).json({
      success: true,
      message: "Offer letter uploaded and released successfully",
      offerLetterUrl: offerLetterUrl,
      contractId: contractId
    });

  } catch (error) {
    console.error("Failed to upload offer letter:", error);
    
    // Clean up uploaded file if database update failed
    if (offerLetterFile?.filepath && fs.existsSync(offerLetterFile.filepath)) {
      try {
        fs.unlinkSync(offerLetterFile.filepath);
      } catch (cleanupError) {
        console.error("Failed to cleanup uploaded file:", cleanupError);
      }
    }

    res.status(500).json({ error: "Failed to upload offer letter" });
  }
});
