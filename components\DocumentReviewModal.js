import { useState, useEffect } from "react";
import { X, Eye, Check, AlertTriangle, FileText, Download } from "lucide-react";

export default function DocumentReviewModal({
  isOpen,
  onClose,
  applicationId,
  onVerify,
  currentUser,
  targetUserType // "seeker" or "referrer" - whose documents are being reviewed
}) {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);

  const documentLabels = {
    aadhar: "Aadhaar Card",
    pan: "PAN Card",
    driving_license: "Driving License",
    passport: "Passport",
  };

  useEffect(() => {
    if (isOpen && applicationId) {
      fetchDocuments();
    }
  }, [isOpen, applicationId]);

  const fetchDocuments = async () => {
    setLoading(true);
    try {
      const res = await fetch(`/api/documents/${applicationId}?targetUserType=${targetUserType}`);
      if (res.ok) {
        const data = await res.json();
        setDocuments(data.documents || []);
      }
    } catch (error) {
      console.error("Failed to fetch documents:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyDocuments = async () => {
    setVerifying(true);
    try {
      await onVerify();
      onClose();
    } catch (error) {
      console.error("Failed to verify documents:", error);
    } finally {
      setVerifying(false);
    }
  };

  const openDocument = (documentUrl) => {
    window.open(documentUrl, '_blank');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <FileText size={24} />
            Verify {targetUserType === "seeker" ? "Job Seeker's" : "Referrer's"} Documents
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-4 border-gray-300 border-t-black rounded-full animate-spin"></div>
            </div>
          ) : (
            <>
              <div className="mb-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="text-blue-600 mt-0.5" size={20} />
                    <div>
                      <h3 className="font-medium text-blue-900 mb-1">Document Review Instructions</h3>
                      <p className="text-blue-800 text-sm">
                        Please carefully review the {targetUserType === "seeker" ? "job seeker's" : "referrer's"} uploaded documents to ensure they are:
                      </p>
                      <ul className="text-blue-800 text-sm mt-2 ml-4 list-disc">
                        <li>Clear and readable</li>
                        <li>Valid and not expired</li>
                        <li>Belong to the correct person</li>
                        <li>Match the information provided</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {documents.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <FileText size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>No documents uploaded yet.</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {Object.keys(documentLabels).map((docType) => {
                    const doc = documents.find(d => d.document_type === docType);
                    return (
                      <div key={docType} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${
                              doc ? 'bg-green-500' : 'bg-gray-300'
                            }`}></div>
                            <div>
                              <h4 className="font-medium">{documentLabels[docType]}</h4>
                              {doc && (
                                <p className="text-sm text-gray-600">
                                  Uploaded on {new Date(doc.uploaded_at).toLocaleDateString()}
                                </p>
                              )}
                            </div>
                          </div>
                          
                          {doc ? (
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => openDocument(doc.document_url)}
                                className="flex items-center gap-2 px-3 py-1.5 text-sm border rounded hover:bg-gray-50 transition-colors"
                              >
                                <Eye size={16} />
                                View
                              </button>
                              <button
                                onClick={() => openDocument(doc.document_url)}
                                className="flex items-center gap-2 px-3 py-1.5 text-sm border rounded hover:bg-gray-50 transition-colors"
                              >
                                <Download size={16} />
                                Download
                              </button>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">Not uploaded</span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {documents.length > 0 && (
                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-yellow-800 text-sm">
                    <strong>Important:</strong> By clicking "Verify Documents", you confirm that you have 
                    reviewed all documents and they meet the verification criteria. This action cannot be undone.
                  </p>
                </div>
              )}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          {documents.length > 0 && (
            <button
              onClick={handleVerifyDocuments}
              disabled={verifying}
              className="flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              {verifying ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Check size={16} />
              )}
              {verifying ? "Verifying..." : "Verify Documents"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
