import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  const { applicationId, targetUserType } = req.query;

  if (req.method === "GET") {
    try {
      // Get documents for the application, optionally filtered by target user type
      let whereClause = "WHERE d.application_id = ?";
      let queryParams = [applicationId];

      if (targetUserType) {
        whereClause += " AND u.user_type = ?";
        queryParams.push(targetUserType);
      }

      const documents = await query(
        `
        SELECT
          d.*,
          u.full_name as uploader_name,
          u.user_type as uploader_type
        FROM documents d
        JOIN users u ON d.user_id = u.id
        ${whereClause}
        ORDER BY d.document_type, d.uploaded_at DESC
      `,
        queryParams
      );

      // Group documents by type (in case there are multiple uploads of same type)
      const groupedDocuments = {};
      documents.forEach(doc => {
        if (!groupedDocuments[doc.document_type] || 
            new Date(doc.uploaded_at) > new Date(groupedDocuments[doc.document_type].uploaded_at)) {
          groupedDocuments[doc.document_type] = doc;
        }
      });

      // Convert back to array
      const latestDocuments = Object.values(groupedDocuments);

      res.status(200).json({ 
        documents: latestDocuments,
        total: latestDocuments.length 
      });
    } catch (error) {
      console.error("Failed to fetch documents:", error);
      res.status(500).json({ error: "Failed to fetch documents" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
});
