-- Database migration to add profile document columns to users table
-- Run this in phpMyAdmin for database: u618120801_SplitJob_33

USE u618120801_SplitJob_33;

-- Add document URL columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS aadhar_url VARCHAR(500) NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS pan_url VARCHAR(500) NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS driving_license_url VARCHAR(500) NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS passport_url VARCHAR(500) NULL;

-- Add document verification status columns
ALTER TABLE users ADD COLUMN IF NOT EXISTS aadhar_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS pan_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS driving_license_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS passport_verified BOOLEAN DEFAULT FALSE;

-- Add document upload timestamps
ALTER TABLE users ADD COLUMN IF NOT EXISTS aadhar_uploaded_at TIMESTAMP NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS pan_uploaded_at TIMESTAMP NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS driving_license_uploaded_at TIMESTAMP NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS passport_uploaded_at TIMESTAMP NULL;

-- Create new table for profile documents (alternative approach for better organization)
CREATE TABLE IF NOT EXISTS profile_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    document_type ENUM('aadhar', 'pan', 'driving_license', 'passport') NOT NULL,
    document_url VARCHAR(500) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_document (user_id, document_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profile_documents_user ON profile_documents(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_documents_type ON profile_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_profile_documents_verified ON profile_documents(is_verified);

-- Update existing documents table to support profile documents (optional)
-- This allows backward compatibility while transitioning to profile-based documents
ALTER TABLE documents ADD COLUMN IF NOT EXISTS is_profile_document BOOLEAN DEFAULT FALSE;

-- Add a comment to track migration
INSERT INTO migrations (name, executed_at) VALUES ('add_profile_documents', NOW()) 
ON DUPLICATE KEY UPDATE executed_at = NOW();

-- Note: You may need to create the migrations table if it doesn't exist:
-- CREATE TABLE IF NOT EXISTS migrations (
--     id INT PRIMARY KEY AUTO_INCREMENT,
--     name VARCHAR(255) UNIQUE NOT NULL,
--     executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );
