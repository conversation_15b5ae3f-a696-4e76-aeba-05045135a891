import { useState, useEffect } from "react";
import { X, AlertCircle, CheckCircle, Info } from "lucide-react";

export default function JobForm({ onSubmit, onCancel, initialData = null }) {
  const [formData, setFormData] = useState({
    title: initialData?.title || "",
    description: initialData?.description || "",
    jobRole: initialData?.job_role || "software_developer",
    skills: initialData?.skills || "",
    experienceYears: initialData?.experience_years || "",
    desiredCompanies: initialData?.desired_companies || "",
    paymentType: initialData?.payment_type || "percentage",
    paymentPercentage: initialData?.payment_percentage || "",
    paymentFixed: initialData?.payment_fixed || "",
    visibility: initialData?.visibility || "public",
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [touched, setTouched] = useState({});

  // Real-time validation
  useEffect(() => {
    if (Object.keys(touched).length > 0) {
      validateForm();
    }
  }, [formData, touched]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    } else if (formData.title.length < 10) {
      newErrors.title = "Title must be at least 10 characters";
    } else if (formData.title.length > 100) {
      newErrors.title = "Title must be less than 100 characters";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    } else if (formData.description.length < 50) {
      newErrors.description = "Description must be at least 50 characters";
    } else if (formData.description.length > 1000) {
      newErrors.description = "Description must be less than 1000 characters";
    }

    if (formData.paymentType === "percentage") {
      const percentage = parseFloat(formData.paymentPercentage);
      if (!percentage || percentage < 1 || percentage > 50) {
        newErrors.paymentPercentage = "Percentage must be between 1 and 50";
      }
    }

    if (formData.paymentType === "fixed") {
      const amount = parseFloat(formData.paymentFixed);
      if (!amount || amount < 1000) {
        newErrors.paymentFixed = "Amount must be at least ₹1,000";
      }
    }

    if (formData.experienceYears && (formData.experienceYears < 0 || formData.experienceYears > 50)) {
      newErrors.experienceYears = "Experience must be between 0 and 50 years";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Mark all fields as touched
    const allFields = Object.keys(formData);
    setTouched(allFields.reduce((acc, field) => ({ ...acc, [field]: true }), {}));

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
    setTouched({ ...touched, [field]: true });
  };

  const getFieldStatus = (field) => {
    if (!touched[field]) return null;
    return errors[field] ? 'error' : 'success';
  };

  const jobRoles = [
    { value: "software_developer", label: "Software Developer" },
    { value: "designer", label: "Designer" },
    { value: "product_manager", label: "Product Manager" },
    { value: "data_scientist", label: "Data Scientist" },
    { value: "marketing", label: "Marketing" },
    { value: "sales", label: "Sales" },
    { value: "other", label: "Other" },
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-in fade-in">
      <div className="bg-background border border-border rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden shadow-lg animate-in slide-up">
        {/* Header */}
        <div className="sticky top-0 bg-background/95 backdrop-blur border-b border-border p-4 sm:p-6 flex justify-between items-center">
          <h2 className="text-xl sm:text-2xl font-semibold">
            {initialData ? "Edit Job Post" : "Create New Job Post"}
          </h2>
          <button
            onClick={onCancel}
            className="btn-ghost p-2 h-auto w-auto"
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        {/* Form */}
        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-6">
            {/* Title */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Job Title <span className="text-destructive">*</span>
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleFieldChange('title', e.target.value)}
                  className={`input ${errors.title ? 'border-destructive focus-visible:ring-destructive' : ''}`}
                  placeholder="e.g., Senior Software Developer needed at top tech companies"
                  maxLength={100}
                />
                {getFieldStatus('title') === 'success' && (
                  <CheckCircle size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-green-500" />
                )}
                {getFieldStatus('title') === 'error' && (
                  <AlertCircle size={16} className="absolute right-3 top-1/2 -translate-y-1/2 text-destructive" />
                )}
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{errors.title || "A clear, descriptive title helps attract the right referrers"}</span>
                <span>{formData.title.length}/100</span>
              </div>
              {errors.title && (
                <p className="text-destructive text-sm flex items-center gap-1">
                  <AlertCircle size={14} />
                  {errors.title}
                </p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Description <span className="text-destructive">*</span>
              </label>
              <div className="relative">
                <textarea
                  rows={6}
                  value={formData.description}
                  onChange={(e) => handleFieldChange('description', e.target.value)}
                  className={`textarea resize-none ${errors.description ? 'border-destructive focus-visible:ring-destructive' : ''}`}
                  placeholder="Describe your background, what kind of role you're looking for, your experience, and what makes you a great candidate..."
                  maxLength={1000}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{errors.description || "Include your background, target role, and what you offer"}</span>
                <span>{formData.description.length}/1000</span>
              </div>
              {errors.description && (
                <p className="text-destructive text-sm flex items-center gap-1">
                  <AlertCircle size={14} />
                  {errors.description}
                </p>
              )}
            </div>

            {/* Job Role */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">Job Role</label>
              <select
                value={formData.jobRole}
                onChange={(e) => handleFieldChange('jobRole', e.target.value)}
                className="select"
              >
                {jobRoles.map((role) => (
                  <option key={role.value} value={role.value}>
                    {role.label}
                  </option>
                ))}
              </select>
              <p className="text-xs text-muted-foreground">
                Select the primary role you're targeting
              </p>
            </div>

            {/* Skills */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">Skills</label>
              <input
                type="text"
                value={formData.skills}
                onChange={(e) => handleFieldChange('skills', e.target.value)}
                className="input"
                placeholder="e.g., React, Node.js, Python, AWS, Machine Learning"
              />
              <p className="text-xs text-muted-foreground">
                Comma-separated list of your key skills and technologies
              </p>
            </div>

            {/* Experience */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Years of Experience
              </label>
              <input
                type="number"
                value={formData.experienceYears}
                onChange={(e) => handleFieldChange('experienceYears', e.target.value)}
                className={`input ${errors.experienceYears ? 'border-destructive focus-visible:ring-destructive' : ''}`}
                min="0"
                max="50"
                placeholder="0"
              />
              <p className="text-xs text-muted-foreground">
                Total years of relevant work experience
              </p>
              {errors.experienceYears && (
                <p className="text-destructive text-sm flex items-center gap-1">
                  <AlertCircle size={14} />
                  {errors.experienceYears}
                </p>
              )}
            </div>

            {/* Target Companies */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Target Companies <span className="text-muted-foreground">(optional)</span>
              </label>
              <input
                type="text"
                value={formData.desiredCompanies}
                onChange={(e) => handleFieldChange('desiredCompanies', e.target.value)}
                className="input"
                placeholder="e.g., Google, Microsoft, Amazon, Meta"
              />
              <p className="text-xs text-muted-foreground">
                Specific companies you're interested in working for
              </p>
            </div>

            {/* Payment Type */}
            <div className="space-y-3">
              <label className="block text-sm font-medium">
                Payment Type <span className="text-destructive">*</span>
              </label>
              <div className="grid gap-3">
                <label className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                  formData.paymentType === "percentage" ? 'border-primary bg-accent' : ''
                }`}>
                  <input
                    type="radio"
                    name="paymentType"
                    value="percentage"
                    checked={formData.paymentType === "percentage"}
                    onChange={(e) => handleFieldChange('paymentType', e.target.value)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="font-medium">Percentage of salary</div>
                    <div className="text-sm text-muted-foreground">
                      Recurring monthly payment based on your salary
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Recommended for long-term partnerships
                    </div>
                  </div>
                </label>
                <label className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                  formData.paymentType === "fixed" ? 'border-primary bg-accent' : ''
                }`}>
                  <input
                    type="radio"
                    name="paymentType"
                    value="fixed"
                    checked={formData.paymentType === "fixed"}
                    onChange={(e) => handleFieldChange('paymentType', e.target.value)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="font-medium">Fixed amount</div>
                    <div className="text-sm text-muted-foreground">
                      One-time payment upon successful placement
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Good for specific referral opportunities
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Payment Amount */}
            {formData.paymentType === "percentage" && (
              <div className="space-y-2">
                <label className="block text-sm font-medium">
                  Percentage of Monthly Salary <span className="text-destructive">*</span>
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={formData.paymentPercentage}
                    onChange={(e) => handleFieldChange('paymentPercentage', e.target.value)}
                    className={`input pr-8 ${errors.paymentPercentage ? 'border-destructive focus-visible:ring-destructive' : ''}`}
                    min="1"
                    max="50"
                    placeholder="10"
                    step="0.5"
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm">
                    %
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  You will pay this percentage of your monthly salary to the referrer. Typical range: 5-15% for most roles
                </p>
                {errors.paymentPercentage && (
                  <p className="text-destructive text-sm flex items-center gap-1">
                    <AlertCircle size={14} />
                    {errors.paymentPercentage}
                  </p>
                )}
              </div>
            )}

            {formData.paymentType === "fixed" && (
              <div className="space-y-2">
                <label className="block text-sm font-medium">
                  Fixed Amount <span className="text-destructive">*</span>
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm">
                    ₹
                  </span>
                  <input
                    type="number"
                    value={formData.paymentFixed}
                    onChange={(e) => handleFieldChange('paymentFixed', e.target.value)}
                    className={`input pl-8 ${errors.paymentFixed ? 'border-destructive focus-visible:ring-destructive' : ''}`}
                    min="1000"
                    placeholder="50000"
                    step="1000"
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  You will pay this fixed amount to the referrer upon successful job placement. Minimum: ₹1,000
                </p>
                {errors.paymentFixed && (
                  <p className="text-destructive text-sm flex items-center gap-1">
                    <AlertCircle size={14} />
                    {errors.paymentFixed}
                  </p>
                )}
              </div>
            )}

            {/* Visibility */}
            <div className="space-y-3">
              <label className="block text-sm font-medium">Visibility</label>
              <div className="grid gap-3">
                <label className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                  formData.visibility === "public" ? 'border-primary bg-accent' : ''
                }`}>
                  <input
                    type="radio"
                    name="visibility"
                    value="public"
                    checked={formData.visibility === "public"}
                    onChange={(e) => handleFieldChange('visibility', e.target.value)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="font-medium">Public</div>
                    <div className="text-sm text-muted-foreground">
                      Visible to everyone on the explore page
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Maximum visibility and reach
                    </div>
                  </div>
                </label>
                <label className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                  formData.visibility === "anonymous" ? 'border-primary bg-accent' : ''
                }`}>
                  <input
                    type="radio"
                    name="visibility"
                    value="anonymous"
                    checked={formData.visibility === "anonymous"}
                    onChange={(e) => handleFieldChange('visibility', e.target.value)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="font-medium">Anonymous</div>
                    <div className="text-sm text-muted-foreground">
                      Your name and profile will be hidden
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Good for privacy while staying discoverable
                    </div>
                  </div>
                </label>
                <label className={`flex items-start gap-3 p-4 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                  formData.visibility === "private" ? 'border-primary bg-accent' : ''
                }`}>
                  <input
                    type="radio"
                    name="visibility"
                    value="private"
                    checked={formData.visibility === "private"}
                    onChange={(e) => handleFieldChange('visibility', e.target.value)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="font-medium">Private</div>
                    <div className="text-sm text-muted-foreground">
                      Only accessible via shareable link
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Share with specific people only
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-border">
              <button
                type="button"
                onClick={onCancel}
                className="btn-outline flex-1 order-2 sm:order-1"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || Object.keys(errors).length > 0}
                className="btn-primary flex-1 order-1 sm:order-2 flex items-center justify-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    {initialData ? "Update Job Post" : "Create Job Post"}
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
