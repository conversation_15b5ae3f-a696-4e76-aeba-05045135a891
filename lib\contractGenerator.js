import { query } from "./db";
import { formatPercentage } from "./utils";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";
import fs from "fs";
import path from "path";

/**
 * Generate a contract for an accepted application
 * @param {number} applicationId - The ID of the accepted application
 * @returns {Object} - Result object with success status, contract ID, and PDF bytes
 */
export async function generateContractForApplication(applicationId) {
  try {
    // Check if contract already exists for this application
    const existingContracts = await query(
      'SELECT id FROM contracts WHERE application_id = ?',
      [applicationId]
    );

    if (existingContracts.length > 0) {
      return {
        success: false,
        error: "Contract already exists for this application",
        contractId: existingContracts[0].id
      };
    }

    // Get application details
    const applications = await query(
      `
      SELECT
        a.*,
        jp.title as job_title,
        jp.payment_type,
        jp.payment_percentage,
        jp.payment_fixed,
        seeker.full_name as seeker_name,
        seeker.email as seeker_email,
        referrer.full_name as referrer_name,
        referrer.email as referrer_email,
        c.seeker_signed,
        c.referrer_signed,
        c.seeker_signed_at,
        c.referrer_signed_at,
        c.seeker_documents_verified,
        c.referrer_documents_verified,
        c.offer_letter_released,
        c.seeker_signature_url,
        c.referrer_signature_url,
        c.status as contract_status
      FROM applications a
      JOIN job_posts jp ON a.job_post_id = jp.id
      JOIN users seeker ON jp.user_id = seeker.id
      JOIN users referrer ON a.referrer_id = referrer.id
      LEFT JOIN contracts c ON c.application_id = a.id
      WHERE a.id = ? AND a.status = 'accepted'
    `,
      [applicationId]
    );

    if (applications.length === 0) {
      return {
        success: false,
        error: "Application not found or not accepted"
      };
    }

    const app = applications[0];

    // Generate PDF
    const pdfBytes = await generateContractPDF(app);

    // Save contract to database
    const result = await query(
      `INSERT INTO contracts (application_id, status, created_at) VALUES (?, 'pending_signatures', NOW())`,
      [applicationId]
    );

    const contractId = result.insertId;

    return {
      success: true,
      contractId: contractId,
      pdfBytes: pdfBytes,
      application: app
    };

  } catch (error) {
    console.error("Failed to generate contract:", error);
    return {
      success: false,
      error: "Failed to generate contract"
    };
  }
}

/**
 * Generate PDF contract document
 * @param {Object} app - Application data with job and user details
 * @returns {Uint8Array} - PDF bytes
 */
export async function generateContractPDF(app) {
  // Create PDF
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([600, 800]);
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  const { width, height } = page.getSize();
  let y = height - 50;

  // Title
  page.drawText("REFERRAL AGREEMENT", {
    x: 200,
    y: y,
    size: 20,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  y -= 40;

  // Agreement content
  const content = [
    `Contract Generated: ${new Date().toLocaleDateString()}`,
    ...(app.contract_status === 'offer_released' ? [`Completed: ${new Date().toLocaleDateString()}`] : []),
    "",
    "PARTIES:",
    `Job Seeker: ${app.seeker_name} (${app.seeker_email})`,
    `Referrer: ${app.referrer_name} (${app.referrer_email})`,
    "",
    "JOB DETAILS:",
    `Position: ${app.job_title}`,
    `Company: ${app.company_name || "To be determined"}`,
    "",
    "COMPENSATION TERMS:",
    app.payment_type === "percentage"
      ? `${formatPercentage(app.payment_percentage)}% of monthly salary (recurring)`
      : `Fixed amount: ₹${app.payment_fixed}`,
    "",
    "TERMS AND CONDITIONS:",
    "1. The Referrer agrees to refer the Job Seeker for the specified position.",
    "2. The Job Seeker agrees to pay the agreed compensation upon successful placement.",
    "3. Payment shall be made within 30 days of joining the company.",
    "4. Both parties agree to maintain confidentiality.",
    "5. This agreement is binding upon successful placement.",
    "",
    "CONTRACT STATUS:",
    ...(app.contract_status ? [
      `Status: ${app.contract_status.toUpperCase()}`,
      ...(app.seeker_documents_verified && app.referrer_documents_verified ? [
        "[COMPLETED] Documents verified by both parties"
      ] : []),
      ...(app.offer_letter_released ? [
        "[COMPLETED] Offer letter released"
      ] : []),
    ] : ["Status: PENDING"]),
    "",
    "SIGNATURES:",
    "",
    // Show actual signature status if available
    ...(app.seeker_signed && app.referrer_signed ? [
      `[SIGNED] ${app.seeker_name}          [SIGNED] ${app.referrer_name}`,
      `Date: ${app.seeker_signed_at ? new Date(app.seeker_signed_at).toLocaleDateString() : 'N/A'}                    Date: ${app.referrer_signed_at ? new Date(app.referrer_signed_at).toLocaleDateString() : 'N/A'}`,
      "",
      "Job Seeker                          Referrer",
    ] : [
      "_____________________               _____________________",
      "Job Seeker                          Referrer",
      "",
      ...(app.seeker_signed ? [`[SIGNED] Job Seeker signed on ${app.seeker_signed_at ? new Date(app.seeker_signed_at).toLocaleDateString() : 'N/A'}`] : []),
      ...(app.referrer_signed ? [`[SIGNED] Referrer signed on ${app.referrer_signed_at ? new Date(app.referrer_signed_at).toLocaleDateString() : 'N/A'}`] : []),
    ]),
  ];

  content.forEach((line) => {
    page.drawText(line, {
      x: 50,
      y: y,
      size: 12,
      font: line.includes(":") ? boldFont : font,
      color: rgb(0, 0, 0),
    });
    y -= 20;
  });

  // Add signature images if available
  if (app.seeker_signature_url || app.referrer_signature_url) {
    y -= 20; // Extra space before signatures

    try {
      // Add seeker signature
      if (app.seeker_signature_url) {
        const seekerSigPath = path.join(process.cwd(), "public", app.seeker_signature_url);
        if (fs.existsSync(seekerSigPath)) {
          const seekerSigBytes = fs.readFileSync(seekerSigPath);
          const seekerSigImage = await pdfDoc.embedPng(seekerSigBytes);
          const seekerSigDims = seekerSigImage.scale(0.3);

          page.drawImage(seekerSigImage, {
            x: 50,
            y: y - seekerSigDims.height,
            width: seekerSigDims.width,
            height: seekerSigDims.height,
          });
        }
      }

      // Add referrer signature
      if (app.referrer_signature_url) {
        const referrerSigPath = path.join(process.cwd(), "public", app.referrer_signature_url);
        if (fs.existsSync(referrerSigPath)) {
          const referrerSigBytes = fs.readFileSync(referrerSigPath);
          const referrerSigImage = await pdfDoc.embedPng(referrerSigBytes);
          const referrerSigDims = referrerSigImage.scale(0.3);

          page.drawImage(referrerSigImage, {
            x: 350,
            y: y - referrerSigDims.height,
            width: referrerSigDims.width,
            height: referrerSigDims.height,
          });
        }
      }
    } catch (error) {
      console.error("Error embedding signature images:", error);
      // Continue without signatures if there's an error
    }
  }

  // Generate PDF bytes
  return await pdfDoc.save();
}

/**
 * Get contract details by application ID
 * @param {number} applicationId - The application ID
 * @returns {Object|null} - Contract details or null if not found
 */
export async function getContractByApplicationId(applicationId) {
  try {
    const contracts = await query(
      `
      SELECT 
        c.*,
        a.referrer_id,
        jp.user_id as seeker_id
      FROM contracts c
      JOIN applications a ON c.application_id = a.id
      JOIN job_posts jp ON a.job_post_id = jp.id
      WHERE c.application_id = ?
    `,
      [applicationId]
    );

    return contracts.length > 0 ? contracts[0] : null;
  } catch (error) {
    console.error("Failed to get contract:", error);
    return null;
  }
}
