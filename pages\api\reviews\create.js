import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { applicationId, rating, reviewText } = req.body;

  try {
    // Verify the application is completed
    const applications = await query(
      `
      SELECT 
        a.*,
        jp.user_id as seeker_id
      FROM applications a
      JOIN job_posts jp ON a.job_post_id = jp.id
      WHERE a.id = ? AND a.status = 'completed'
    `,
      [applicationId]
    );

    if (applications.length === 0) {
      return res
        .status(400)
        .json({ error: "Application not found or not completed" });
    }

    const app = applications[0];

    // Determine who is being reviewed
    const reviewedUserId =
      req.user.id === app.seeker_id ? app.referrer_id : app.seeker_id;

    // Check if already reviewed
    const existing = await query(
      `SELECT id FROM reviews 
       WHERE application_id = ? AND reviewer_id = ? AND reviewed_user_id = ?`,
      [applicationId, req.user.id, reviewedUserId]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: "Already reviewed" });
    }

    // Create review
    await query(
      `INSERT INTO reviews (
        application_id, reviewer_id, reviewed_user_id, rating, review_text
      ) VALUES (?, ?, ?, ?, ?)`,
      [applicationId, req.user.id, reviewedUserId, rating, reviewText]
    );

    res.status(201).json({ success: true });
  } catch (error) {
    console.error("Failed to create review:", error);
    res.status(500).json({ error: "Failed to create review" });
  }
});
