const mysql = require("mysql2/promise");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const fs = require('fs');

// Load environment variables manually
const envContent = fs.readFileSync('.env.local', 'utf8');
const envLines = envContent.split('\n');
envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    let value = valueParts.join('=');
    // Remove quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) || 
        (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }
    process.env[key] = value;
  }
});

async function testAdminLogin() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('🧪 Testing admin login process...');

    const email = '<EMAIL>';
    const password = 'AdminBro';

    // Step 1: Find user (simulating login API)
    console.log('1️⃣ Looking up user by email...');
    const [users] = await connection.execute('SELECT * FROM users WHERE email = ?', [email]);
    
    if (users.length === 0) {
      console.log('❌ User not found');
      return;
    }

    const user = users[0];
    console.log('✅ User found:', {
      id: user.id,
      email: user.email,
      user_type: user.user_type,
      full_name: user.full_name
    });

    // Step 2: Verify password
    console.log('2️⃣ Verifying password...');
    const isValid = await bcrypt.compare(password, user.password_hash);
    console.log('Password verification:', isValid ? '✅ VALID' : '❌ INVALID');

    if (!isValid) {
      console.log('❌ Login would fail - invalid password');
      return;
    }

    // Step 3: Generate JWT token
    console.log('3️⃣ Generating JWT token...');
    const token = jwt.sign(
      { userId: user.id, userType: user.user_type }, 
      process.env.JWT_SECRET, 
      { expiresIn: "7d" }
    );
    console.log('✅ Token generated:', token.substring(0, 50) + '...');

    // Step 4: Verify token
    console.log('4️⃣ Verifying token...');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('✅ Token verified:', decoded);

    console.log('\n🎉 LOGIN TEST SUCCESSFUL!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: AdminBro');
    console.log('🎫 Token: Valid');
    console.log('👤 User Type: admin');

    await connection.end();
    
  } catch (error) {
    console.error('❌ Login test failed:', error);
  }
}

testAdminLogin();
