# Split Job - NextJS Application

## Project Structure
```
split-job/
├── pages/
│   ├── api/
│   │   ├── auth/
│   │   │   ├── login.js
│   │   │   ├── signup.js
│   │   │   ├── logout.js
│   │   │   └── reset-password.js
│   │   ├── jobs/
│   │   │   ├── index.js
│   │   │   ├── [id].js
│   │   │   └── create.js
│   │   ├── applications/
│   │   │   ├── index.js
│   │   │   ├── [id].js
│   │   │   └── apply.js
│   │   ├── contracts/
│   │   │   ├── generate.js
│   │   │   └── sign.js
│   │   ├── documents/
│   │   │   └── upload.js
│   │   ├── reviews/
│   │   │   └── create.js
│   │   └── users/
│   │       └── profile.js
│   ├── _app.js
│   ├── _document.js
│   ├── index.js
│   ├── explore.js
│   ├── login.js
│   ├── signup.js
│   ├── dashboard.js
│   ├── job/[id].js
│   └── profile.js
├── components/
│   ├── Layout.js
│   ├── Navbar.js
│   ├── JobCard.js
│   ├── JobForm.js
│   ├── ApplicationCard.js
│   ├── ContractViewer.js
│   ├── DocumentUpload.js
│   ├── ReviewForm.js
│   └── FilterSidebar.js
├── lib/
│   ├── db.js
│   ├── auth.js
│   ├── middleware.js
│   └── utils.js
├── styles/
│   └── globals.css
├── public/
├── package.json
└── next.config.js
```