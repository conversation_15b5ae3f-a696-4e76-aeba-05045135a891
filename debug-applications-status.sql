-- Debug query to check applications and contracts status
-- Run this in phpMyAdmin to see what's happening with your data

USE u618120801_SplitJob_33;

-- Check current applications and their contract status
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.id as contract_id,
    c.status as contract_status,
    c.seeker_signed,
    c.referrer_signed,
    c.seeker_documents_verified,
    c.referrer_documents_verified,
    c.offer_letter_released,
    jp.title as job_title
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
LEFT JOIN job_posts jp ON a.job_post_id = jp.id
WHERE a.status IN ('accepted', 'in_progress', 'completed')
ORDER BY a.id DESC
LIMIT 20;

-- Check for applications that should be completed but aren't
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.status as contract_status,
    c.offer_letter_released,
    'Should be completed' as issue
FROM applications a
JOIN contracts c ON c.application_id = a.id
WHERE c.offer_letter_released = TRUE 
  AND a.status != 'completed';

-- Check for contracts with offer released but wrong status
SELECT 
    c.id as contract_id,
    c.status as contract_status,
    c.offer_letter_released,
    a.status as app_status,
    'Contract status should be offer_released' as issue
FROM contracts c
JOIN applications a ON c.application_id = a.id
WHERE c.offer_letter_released = TRUE 
  AND c.status != 'offer_released';
