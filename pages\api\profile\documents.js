import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import formidable from "formidable";
import fs from "fs";
import path from "path";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default withAuth(async (req, res) => {
  if (req.method === "GET") {
    // Get user's profile documents
    try {
      const documents = await query(
        `SELECT 
          document_type, 
          document_url, 
          is_verified, 
          uploaded_at,
          updated_at
        FROM profile_documents 
        WHERE user_id = ?
        ORDER BY document_type`,
        [req.user.id]
      );

      // Convert to object format for easier frontend handling
      const documentsObj = {};
      documents.forEach(doc => {
        documentsObj[doc.document_type] = {
          url: doc.document_url,
          verified: doc.is_verified,
          uploadedAt: doc.uploaded_at,
          updatedAt: doc.updated_at
        };
      });

      res.status(200).json({ documents: documentsObj });
    } catch (error) {
      console.error("Failed to fetch profile documents:", error);
      res.status(500).json({ error: "Failed to fetch documents" });
    }
  } else if (req.method === "POST") {
    // Upload or update a profile document
    try {
      // Create upload directory if it doesn't exist
      const uploadDir = "./public/uploads/profile";
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // Parse multipart form data using formidable
      const form = formidable({
        uploadDir: uploadDir,
        keepExtensions: true,
        maxFileSize: 5 * 1024 * 1024, // 5MB limit
      });

      form.parse(req, async (err, fields, files) => {
        try {
          if (err) {
            console.error("Formidable parse error:", err);
            return res.status(400).json({ error: "Failed to parse form data" });
          }

          const documentType = Array.isArray(fields.documentType) ? fields.documentType[0] : fields.documentType;
          const file = Array.isArray(files.document) ? files.document[0] : files.document;

          if (!documentType || !file) {
            return res.status(400).json({ error: "Missing required fields" });
          }

          // Validate document type
          const allowedTypes = ['aadhar', 'pan', 'driving_license', 'passport'];
          if (!allowedTypes.includes(documentType)) {
            return res.status(400).json({ error: "Invalid document type" });
          }

          // Validate file type
          const allowedMimeTypes = [
            "image/jpeg",
            "image/png",
            "image/jpg",
            "application/pdf",
          ];

          if (!allowedMimeTypes.includes(file.mimetype)) {
            return res.status(400).json({ error: "Invalid file type. Only JPG, PNG, and PDF files are allowed." });
          }

          // Generate unique filename
          const timestamp = Date.now();
          const fileExtension = path.extname(file.originalFilename || file.newFilename);
          const uniqueFileName = `${req.user.id}_${documentType}_${timestamp}${fileExtension}`;
          const newFilePath = path.join(uploadDir, uniqueFileName);

          // Move file to final location with unique name
          fs.renameSync(file.filepath, newFilePath);

          const documentUrl = `/uploads/profile/${uniqueFileName}`;

          // Check if document already exists for this user and type
          const existingDocs = await query(
            "SELECT id FROM profile_documents WHERE user_id = ? AND document_type = ?",
            [req.user.id, documentType]
          );

          if (existingDocs.length > 0) {
            // Update existing document
            await query(
              `UPDATE profile_documents
               SET document_url = ?, is_verified = FALSE, updated_at = NOW()
               WHERE user_id = ? AND document_type = ?`,
              [documentUrl, req.user.id, documentType]
            );
          } else {
            // Insert new document
            await query(
              `INSERT INTO profile_documents (user_id, document_type, document_url)
               VALUES (?, ?, ?)`,
              [req.user.id, documentType, documentUrl]
            );
          }

          res.status(200).json({
            success: true,
            url: documentUrl,
            message: "Document uploaded successfully"
          });

        } catch (error) {
          console.error("Failed to process upload:", error);
          res.status(500).json({
            error: "Failed to process upload",
            details: error.message
          });
        }
      });

    } catch (error) {
      console.error("Upload error:", error);
      res.status(500).json({ error: "Failed to upload document" });
    }
  } else if (req.method === "DELETE") {
    // Delete a profile document
    try {
      const { documentType } = req.query;

      if (!documentType) {
        return res.status(400).json({ error: "Document type is required" });
      }

      // Get document info before deletion
      const documents = await query(
        "SELECT document_url FROM profile_documents WHERE user_id = ? AND document_type = ?",
        [req.user.id, documentType]
      );

      if (documents.length === 0) {
        return res.status(404).json({ error: "Document not found" });
      }

      // Delete from database
      await query(
        "DELETE FROM profile_documents WHERE user_id = ? AND document_type = ?",
        [req.user.id, documentType]
      );

      // Delete file from disk (optional - you might want to keep files for audit)
      try {
        const filePath = path.join("./public", documents[0].document_url);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      } catch (fileError) {
        console.warn("Failed to delete file from disk:", fileError);
      }

      res.status(200).json({ 
        success: true, 
        message: "Document deleted successfully" 
      });

    } catch (error) {
      console.error("Failed to delete document:", error);
      res.status(500).json({ error: "Failed to delete document" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
});
