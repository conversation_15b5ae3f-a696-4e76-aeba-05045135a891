import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { query } from "./db";

export async function hashPassword(password) {
  return bcrypt.hash(password, 10);
}

export async function verifyPassword(password, hashedPassword) {
  return bcrypt.compare(password, hashedPassword);
}

export function generateToken(userId, userType) {
  return jwt.sign({ userId, userType }, process.env.JWT_SECRET, {
    expiresIn: "7d",
  });
}

export function verifyToken(token) {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export async function getUserFromToken(token) {
  const decoded = verifyToken(token);
  if (!decoded) return null;

  const [user] = await query(
    "SELECT id, email, user_type, full_name FROM users WHERE id = ?",
    [decoded.userId]
  );

  return user || null;
}
