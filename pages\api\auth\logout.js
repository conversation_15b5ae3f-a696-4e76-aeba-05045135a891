export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Clear cookie by setting it to expire in the past
    const isProduction = process.env.NODE_ENV === "production";
    const cookieString = [
      "token=",
      "Path=/",
      "Expires=Thu, 01 Jan 1970 00:00:00 GMT",
      "HttpOnly",
      "SameSite=Lax",
      isProduction ? "Secure" : ""
    ].filter(Boolean).join("; ");

    res.setHeader("Set-Cookie", cookieString);
    res.status(200).json({ message: "Logged out successfully" });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({ error: "Failed to logout" });
  }
}
