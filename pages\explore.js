import { useState, useEffect } from "react";
import Layout from "../components/Layout";
import JobCard from "../components/JobCard";
import FilterSidebar from "../components/FilterSidebar";
import { Search, Filter } from "lucide-react";

export default function Explore({ user }) {
  const [jobs, setJobs] = useState([]);
  const [filteredJobs, setFilteredJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    userType: "all",
    paymentType: "all",
    jobRole: "all",
    minPercentage: "",
    maxPercentage: "",
    minFixed: "",
    maxFixed: "",
  });

  useEffect(() => {
    fetchJobs();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [jobs, filters, searchTerm]);

  const fetchJobs = async () => {
    try {
      const res = await fetch("/api/jobs");
      const data = await res.json();
      setJobs(data.jobs || []);
    } catch (error) {
      console.error("Failed to fetch jobs:", error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...jobs];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (job) =>
          job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          job.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          job.job_role.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // User type filter
    if (filters.userType !== "all") {
      filtered = filtered.filter((job) => job.user_type === filters.userType);
    }

    // Payment type filter
    if (filters.paymentType !== "all") {
      filtered = filtered.filter(
        (job) => job.payment_type === filters.paymentType
      );
    }

    // Job role filter
    if (filters.jobRole !== "all") {
      filtered = filtered.filter((job) => job.job_role === filters.jobRole);
    }

    // Percentage range filter
    if (filters.paymentType === "percentage" || filters.paymentType === "all") {
      if (filters.minPercentage) {
        filtered = filtered.filter(
          (job) =>
            job.payment_type === "percentage" &&
            job.payment_percentage >= parseFloat(filters.minPercentage)
        );
      }
      if (filters.maxPercentage) {
        filtered = filtered.filter(
          (job) =>
            job.payment_type === "percentage" &&
            job.payment_percentage <= parseFloat(filters.maxPercentage)
        );
      }
    }

    // Fixed amount range filter
    if (filters.paymentType === "fixed" || filters.paymentType === "all") {
      if (filters.minFixed) {
        filtered = filtered.filter(
          (job) =>
            job.payment_type === "fixed" &&
            job.payment_fixed >= parseFloat(filters.minFixed)
        );
      }
      if (filters.maxFixed) {
        filtered = filtered.filter(
          (job) =>
            job.payment_type === "fixed" &&
            job.payment_fixed <= parseFloat(filters.maxFixed)
        );
      }
    }

    setFilteredJobs(filtered);
  };

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">Explore Opportunities</h1>

            {/* Search Bar */}
            <div className="flex gap-4 mb-6">
              <div className="flex-1 relative">
                <Search
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={20}
                />
                <input
                  type="text"
                  placeholder="Search by title, role, or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  style={{ backgroundColor: '#F9FAFB' }}
                />
              </div>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="px-6 py-3 border rounded-lg hover:bg-gray-100 transition-colors flex items-center gap-2 md:hidden"
              >
                <Filter size={20} />
                Filters
              </button>
            </div>
          </div>

          <div className="flex gap-8">
            {/* Filters Sidebar - Desktop */}
            <div className="hidden md:block w-64">
              <FilterSidebar filters={filters} setFilters={setFilters} />
            </div>

            {/* Mobile Filters */}
            {showFilters && (
              <div className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden">
                <div className="bg-white w-80 h-full overflow-y-auto">
                  <div className="p-4 border-b flex justify-between items-center">
                    <h2 className="text-lg font-semibold">Filters</h2>
                    <button onClick={() => setShowFilters(false)}>✕</button>
                  </div>
                  <div className="p-4">
                    <FilterSidebar filters={filters} setFilters={setFilters} />
                  </div>
                </div>
              </div>
            )}

            {/* Job Listings */}
            <div className="flex-1">
              {loading ? (
                <div className="text-center py-12">Loading...</div>
              ) : filteredJobs.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  No opportunities found matching your criteria.
                </div>
              ) : (
                <div className="grid gap-4">
                  {filteredJobs.map((job) => (
                    <JobCard key={job.id} job={job} user={user} />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
