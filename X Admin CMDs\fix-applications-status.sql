-- Fix applications and contracts status
-- Run this in phpMyAdmin to fix the status issues

USE u618120801_SplitJob_33;

-- 1. Fix applications that should be completed (where offer letter is released)
UPDATE applications a
JOIN contracts c ON c.application_id = a.id
SET a.status = 'completed'
WHERE c.offer_letter_released = TRUE 
  AND a.status != 'completed';

-- 2. Fix contract status for contracts where offer letter is released
UPDATE contracts 
SET status = 'offer_released'
WHERE offer_letter_released = TRUE 
  AND status != 'offer_released';

-- 3. Fix contract status for contracts where both parties verified documents but no offer released
UPDATE contracts 
SET status = 'documents_verified'
WHERE seeker_documents_verified = TRUE 
  AND referrer_documents_verified = TRUE
  AND (offer_letter_released IS NULL OR offer_letter_released = FALSE)
  AND status != 'documents_verified';

-- 4. Fix contract status for contracts where both parties signed but documents not verified
UPDATE contracts 
SET status = 'signed'
WHERE seeker_signed = TRUE 
  AND referrer_signed = TRUE
  AND (seeker_documents_verified IS NULL OR seeker_documents_verified = FALSE 
       OR referrer_documents_verified IS NULL OR referrer_documents_verified = FALSE)
  AND status != 'signed';

-- 5. Fix contract status for contracts where only some parties signed
UPDATE contracts 
SET status = 'pending_signatures'
WHERE (seeker_signed = TRUE OR referrer_signed = TRUE)
  AND NOT (seeker_signed = TRUE AND referrer_signed = TRUE)
  AND status != 'pending_signatures';

-- 6. Fix applications that should be in_progress (where contract exists and is signed)
UPDATE applications a
JOIN contracts c ON c.application_id = a.id
SET a.status = 'in_progress'
WHERE c.status IN ('signed', 'documents_pending', 'documents_verified')
  AND a.status = 'accepted';

-- Show results after fix
SELECT 'AFTER FIX - Applications Status:' as info;
SELECT 
    status,
    COUNT(*) as count
FROM applications 
GROUP BY status;

SELECT 'AFTER FIX - Contracts Status:' as info;
SELECT 
    status,
    COUNT(*) as count
FROM contracts 
GROUP BY status;

SELECT 'AFTER FIX - Applications with contracts:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.status as contract_status,
    c.offer_letter_released,
    jp.title as job_title
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
LEFT JOIN job_posts jp ON a.job_post_id = jp.id
WHERE a.status IN ('accepted', 'in_progress', 'completed')
ORDER BY a.id DESC
LIMIT 10;
