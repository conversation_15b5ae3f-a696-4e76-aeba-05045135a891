-- Migration script to fix status for old contracts
-- This ensures backward compatibility for existing contracts

USE u618120801_SplitJob_33;

-- First, add the status column if it doesn't exist
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS status ENUM('draft', 'pending_signatures', 'signed', 'documents_pending', 'documents_verified', 'offer_released', 'cancelled') DEFAULT 'pending_signatures';

-- Update status for contracts that don't have a proper status set
-- This handles old contracts that were created before the status column existed

-- 1. Set offer_released status for contracts where offer letter is released
UPDATE contracts 
SET status = 'offer_released' 
WHERE (status IS NULL OR status = 'draft' OR status = 'pending_signatures') 
  AND offer_letter_released = TRUE;

-- 2. Set documents_verified status for contracts where both parties verified documents
UPDATE contracts 
SET status = 'documents_verified' 
WHERE (status IS NULL OR status = 'draft' OR status = 'pending_signatures') 
  AND seeker_documents_verified = TRUE 
  AND referrer_documents_verified = TRUE
  AND (offer_letter_released IS NULL OR offer_letter_released = FALSE);

-- 3. Set signed status for contracts where both parties signed but documents not verified
UPDATE contracts 
SET status = 'signed' 
WHERE (status IS NULL OR status = 'draft' OR status = 'pending_signatures') 
  AND seeker_signed = TRUE 
  AND referrer_signed = TRUE
  AND (seeker_documents_verified IS NULL OR seeker_documents_verified = FALSE OR referrer_documents_verified IS NULL OR referrer_documents_verified = FALSE);

-- 4. Set pending_signatures for contracts where at least one party signed
UPDATE contracts 
SET status = 'pending_signatures' 
WHERE (status IS NULL OR status = 'draft') 
  AND (seeker_signed = TRUE OR referrer_signed = TRUE)
  AND NOT (seeker_signed = TRUE AND referrer_signed = TRUE);

-- 5. Ensure all remaining contracts have pending_signatures status
UPDATE contracts 
SET status = 'pending_signatures' 
WHERE status IS NULL OR status = 'draft';

-- Show the results
SELECT 
    status,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM contracts), 2), '%') as percentage
FROM contracts 
GROUP BY status
ORDER BY count DESC;

SELECT "Old contracts status migration completed successfully!" as message;
