import { query } from "./db";
import { formatPercentage } from "./utils";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";

/**
 * Generate a contract for an accepted application
 * @param {number} applicationId - The ID of the accepted application
 * @returns {Object} - Result object with success status, contract ID, and PDF bytes
 */
export async function generateContractForApplication(applicationId) {
  try {
    // Check if contract already exists for this application
    const existingContracts = await query(
      'SELECT id FROM contracts WHERE application_id = ?',
      [applicationId]
    );

    if (existingContracts.length > 0) {
      return {
        success: false,
        error: "Contract already exists for this application",
        contractId: existingContracts[0].id
      };
    }

    // Get application details
    const applications = await query(
      `
      SELECT 
        a.*,
        jp.title as job_title,
        jp.payment_type,
        jp.payment_percentage,
        jp.payment_fixed,
        seeker.full_name as seeker_name,
        seeker.email as seeker_email,
        referrer.full_name as referrer_name,
        referrer.email as referrer_email
      FROM applications a
      JOIN job_posts jp ON a.job_post_id = jp.id
      JOIN users seeker ON jp.user_id = seeker.id
      JOIN users referrer ON a.referrer_id = referrer.id
      WHERE a.id = ? AND a.status = 'accepted'
    `,
      [applicationId]
    );

    if (applications.length === 0) {
      return {
        success: false,
        error: "Application not found or not accepted"
      };
    }

    const app = applications[0];

    // Generate PDF
    const pdfBytes = await generateContractPDF(app);

    // Save contract to database
    const result = await query(
      `INSERT INTO contracts (application_id, status, created_at) VALUES (?, 'pending_signatures', NOW())`,
      [applicationId]
    );

    const contractId = result.insertId;

    return {
      success: true,
      contractId: contractId,
      pdfBytes: pdfBytes,
      application: app
    };

  } catch (error) {
    console.error("Failed to generate contract:", error);
    return {
      success: false,
      error: "Failed to generate contract"
    };
  }
}

/**
 * Generate PDF contract document
 * @param {Object} app - Application data with job and user details
 * @returns {Uint8Array} - PDF bytes
 */
async function generateContractPDF(app) {
  // Create PDF
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([600, 800]);
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  const { width, height } = page.getSize();
  let y = height - 50;

  // Title
  page.drawText("REFERRAL AGREEMENT", {
    x: 200,
    y: y,
    size: 20,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  y -= 40;

  // Agreement content
  const content = [
    `Date: ${new Date().toLocaleDateString()}`,
    "",
    "PARTIES:",
    `Job Seeker: ${app.seeker_name} (${app.seeker_email})`,
    `Referrer: ${app.referrer_name} (${app.referrer_email})`,
    "",
    "JOB DETAILS:",
    `Position: ${app.job_title}`,
    `Company: ${app.company_name || "To be determined"}`,
    "",
    "COMPENSATION TERMS:",
    app.payment_type === "percentage"
      ? `${formatPercentage(app.payment_percentage)}% of monthly salary (recurring)`
      : `Fixed amount: ₹${app.payment_fixed}`,
    "",
    "TERMS AND CONDITIONS:",
    "1. The Referrer agrees to refer the Job Seeker for the specified position.",
    "2. The Job Seeker agrees to pay the agreed compensation upon successful placement.",
    "3. Payment shall be made within 30 days of joining the company.",
    "4. Both parties agree to maintain confidentiality.",
    "5. This agreement is binding upon successful placement.",
    "",
    "SIGNATURES:",
    "",
    "_____________________               _____________________",
    "Job Seeker                          Referrer",
  ];

  content.forEach((line) => {
    page.drawText(line, {
      x: 50,
      y: y,
      size: 12,
      font: line.includes(":") ? boldFont : font,
      color: rgb(0, 0, 0),
    });
    y -= 20;
  });

  // Generate PDF bytes
  return await pdfDoc.save();
}

/**
 * Get contract details by application ID
 * @param {number} applicationId - The application ID
 * @returns {Object|null} - Contract details or null if not found
 */
export async function getContractByApplicationId(applicationId) {
  try {
    const contracts = await query(
      `
      SELECT 
        c.*,
        a.referrer_id,
        jp.user_id as seeker_id
      FROM contracts c
      JOIN applications a ON c.application_id = a.id
      JOIN job_posts jp ON a.job_post_id = jp.id
      WHERE c.application_id = ?
    `,
      [applicationId]
    );

    return contracts.length > 0 ? contracts[0] : null;
  } catch (error) {
    console.error("Failed to get contract:", error);
    return null;
  }
}
