import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import { generateContractForApplication } from "../../../lib/contractGenerator";

export default withAuth(async (req, res) => {
  const { id } = req.query;

  if (req.method === "GET") {
    try {
      const applications = await query(
        `
        SELECT 
          a.*,
          jp.title as job_title,
          jp.user_id as seeker_id,
          c.id as contract_id
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        LEFT JOIN contracts c ON c.application_id = a.id
        WHERE a.id = ?
      `,
        [id]
      );

      if (applications.length === 0) {
        return res.status(404).json({ error: "Application not found" });
      }

      const application = applications[0];

      // Verify user has access
      if (
        req.user.id !== application.seeker_id &&
        req.user.id !== application.referrer_id
      ) {
        return res.status(403).json({ error: "Access denied" });
      }

      res.status(200).json({ application });
    } catch (error) {
      console.error("Failed to fetch application:", error);
      res.status(500).json({ error: "Failed to fetch application" });
    }
  } else if (req.method === "PATCH") {
    try {
      const { action } = req.body;

      // Get application details first
      const applications = await query(
        `
        SELECT
          a.*,
          jp.title as job_title,
          jp.user_id as seeker_id
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        WHERE a.id = ?
      `,
        [id]
      );

      if (applications.length === 0) {
        return res.status(404).json({ error: "Application not found" });
      }

      const application = applications[0];

      // Verify user has permission to update this application
      if (req.user.id !== application.seeker_id) {
        return res.status(403).json({ error: "Only the job poster can update applications" });
      }

      // Update application status based on action
      let newStatus;
      switch (action) {
        case 'accept':
          newStatus = 'accepted';
          break;
        case 'reject':
          newStatus = 'rejected';
          break;
        default:
          return res.status(400).json({ error: "Invalid action" });
      }

      // Update the application
      await query(
        "UPDATE applications SET status = ?, updated_at = NOW() WHERE id = ?",
        [newStatus, id]
      );

      let contractId = null;
      let contractGenerated = false;

      // If accepting, automatically generate contract
      if (action === 'accept') {
        try {
          const contractResult = await generateContractForApplication(id);
          if (contractResult.success) {
            contractId = contractResult.contractId;
            contractGenerated = true;
          } else {
            console.warn("Contract generation failed:", contractResult.error);
            // Don't fail the acceptance if contract generation fails
            // User can manually generate later
          }
        } catch (error) {
          console.error("Contract generation error:", error);
          // Don't fail the acceptance if contract generation fails
        }
      }

      // Send notification to referrer
      const notificationMessage = newStatus === 'accepted'
        ? (contractGenerated
          ? `Your application for "${application.job_title}" has been accepted and a contract has been generated. Please review and sign the contract.`
          : `Your application for "${application.job_title}" has been accepted.`)
        : `Your application for "${application.job_title}" has been rejected.`;

      await query(
        `INSERT INTO notifications (
          user_id, type, title, message, related_id, created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())`,
        [
          application.referrer_id,
          `application_${newStatus}`,
          `Application ${newStatus === 'accepted' ? 'Accepted' : 'Rejected'}`,
          notificationMessage,
          application.id
        ]
      );

      res.status(200).json({
        message: `Application ${newStatus} successfully`,
        status: newStatus,
        contractId: contractId,
        contractGenerated: contractGenerated
      });
    } catch (error) {
      console.error("Failed to update application:", error);
      res.status(500).json({ error: "Failed to update application" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
});
