import { query } from "../../../lib/db";

export default async function handler(req, res) {
  if (req.method === "GET") {
    try {
      const jobs = await query(`
        SELECT 
          jp.*,
          u.full_name as user_name,
          u.user_type,
          COUNT(DISTINCT a.id) as applications_count
        FROM job_posts jp
        JOIN users u ON jp.user_id = u.id
        LEFT JOIN applications a ON jp.id = a.job_post_id
        WHERE jp.status = 'active' 
          AND (jp.visibility = 'public' OR jp.visibility = 'anonymous')
        GROUP BY jp.id
        ORDER BY jp.created_at DESC
      `);

      res.status(200).json({ jobs });
    } catch (error) {
      console.error("Failed to fetch jobs:", error);
      res.status(500).json({ error: "Failed to fetch jobs" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
}
