import { query } from "../../../lib/db";
import { hashPassword } from "../../../lib/auth";
import { v4 as uuidv4 } from "uuid";
import cookie from "cookie";

export default async function handler(req, res) {
  if (req.method === "POST") {
    const { action, email, token, newPassword } = req.body;

    try {
      if (action === "request") {
        // Request password reset
        if (!email) {
          return res.status(400).json({ error: "Email is required" });
        }

        // Check if user exists
        const users = await query(
          "SELECT id, full_name FROM users WHERE email = ?",
          [email]
        );

        if (users.length === 0) {
          // Don't reveal if email exists
          return res.status(200).json({
            message:
              "If an account exists with this email, you will receive a password reset link.",
          });
        }

        const user = users[0];
        const resetToken = uuidv4();
        const expiresAt = new Date(Date.now() + 3600000); // 1 hour from now

        // Save reset token
        await query(
          "INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)",
          [user.id, resetToken, expiresAt]
        );

        // In a real application, you would send an email here
        // For now, we'll return the token (remove this in production!)
        console.log(
          `Password reset link: ${process.env.NEXT_PUBLIC_API_URL}/reset-password?token=${resetToken}`
        );

        res.status(200).json({
          message: "Password reset link has been sent to your email.",
          // Remove this in production - only for development
          resetLink: `/reset-password?token=${resetToken}`,
        });
      } else if (action === "reset") {
        // Reset password with token
        if (!token || !newPassword) {
          return res
            .status(400)
            .json({ error: "Token and new password are required" });
        }

        if (newPassword.length < 6) {
          return res
            .status(400)
            .json({ error: "Password must be at least 6 characters" });
        }

        // Verify token
        const resets = await query(
          `SELECT pr.*, u.email 
           FROM password_resets pr 
           JOIN users u ON pr.user_id = u.id 
           WHERE pr.token = ? AND pr.expires_at > NOW()`,
          [token]
        );

        if (resets.length === 0) {
          return res
            .status(400)
            .json({ error: "Invalid or expired reset token" });
        }

        const reset = resets[0];

        // Hash new password
        const passwordHash = await hashPassword(newPassword);

        // Update user password
        await query("UPDATE users SET password_hash = ? WHERE id = ?", [
          passwordHash,
          reset.user_id,
        ]);

        // Delete used reset token
        await query("DELETE FROM password_resets WHERE token = ?", [token]);

        res.status(200).json({ message: "Password reset successfully" });
      } else if (action === "verify") {
        // Verify reset token
        if (!token) {
          return res.status(400).json({ error: "Token is required" });
        }

        const resets = await query(
          "SELECT * FROM password_resets WHERE token = ? AND expires_at > NOW()",
          [token]
        );

        if (resets.length === 0) {
          return res
            .status(400)
            .json({ error: "Invalid or expired reset token" });
        }

        res.status(200).json({ valid: true });
      } else {
        res.status(400).json({ error: "Invalid action" });
      }
    } catch (error) {
      console.error("Password reset error:", error);
      res.status(500).json({ error: "Failed to process password reset" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
}