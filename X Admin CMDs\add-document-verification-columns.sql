-- Migration script to add document verification columns to contracts table
-- Run this script to fix the "Unknown column 'seeker_documents_verified'" error

USE split_job;

-- Check if columns already exist before adding them
SET @sql = '';

-- Add seeker_documents_verified column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'split_job' 
  AND TABLE_NAME = 'contracts' 
  AND COLUMN_NAME = 'seeker_documents_verified';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE contracts ADD COLUMN seeker_documents_verified BOOLEAN DEFAULT FALSE;', 
  'SELECT "Column seeker_documents_verified already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add referrer_documents_verified column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'split_job' 
  AND TABLE_NAME = 'contracts' 
  AND COLUMN_NAME = 'referrer_documents_verified';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE contracts ADD COLUMN referrer_documents_verified BOOLEAN DEFAULT FALSE;', 
  'SELECT "Column referrer_documents_verified already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add seeker_verified_at column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'split_job' 
  AND TABLE_NAME = 'contracts' 
  AND COLUMN_NAME = 'seeker_verified_at';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE contracts ADD COLUMN seeker_verified_at TIMESTAMP NULL;', 
  'SELECT "Column seeker_verified_at already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add referrer_verified_at column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'split_job' 
  AND TABLE_NAME = 'contracts' 
  AND COLUMN_NAME = 'referrer_verified_at';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE contracts ADD COLUMN referrer_verified_at TIMESTAMP NULL;', 
  'SELECT "Column referrer_verified_at already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add offer_letter_released column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'split_job' 
  AND TABLE_NAME = 'contracts' 
  AND COLUMN_NAME = 'offer_letter_released';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE contracts ADD COLUMN offer_letter_released BOOLEAN DEFAULT FALSE;', 
  'SELECT "Column offer_letter_released already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add offer_letter_url column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'split_job' 
  AND TABLE_NAME = 'contracts' 
  AND COLUMN_NAME = 'offer_letter_url';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE contracts ADD COLUMN offer_letter_url VARCHAR(500);', 
  'SELECT "Column offer_letter_url already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add offer_letter_released_at column if it doesn't exist
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'split_job' 
  AND TABLE_NAME = 'contracts' 
  AND COLUMN_NAME = 'offer_letter_released_at';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE contracts ADD COLUMN offer_letter_released_at TIMESTAMP NULL;', 
  'SELECT "Column offer_letter_released_at already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show the final structure of the contracts table
DESCRIBE contracts;

SELECT "Migration completed successfully!" as message;
