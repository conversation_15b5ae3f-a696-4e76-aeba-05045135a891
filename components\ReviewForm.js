import { useState, useEffect } from "react";
import { Star, MessageSquare, AlertCircle, CheckCircle } from "lucide-react";

export default function ReviewForm({ applicationId, onSubmit, className = "" }) {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [reviewText, setReviewText] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [existingReview, setExistingReview] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkExistingReview = async () => {
      try {
        const res = await fetch(`/api/reviews/check?applicationId=${applicationId}`);
        if (res.ok) {
          const data = await res.json();
          if (data.hasReviewed) {
            setExistingReview(data.review);
          }
        }
      } catch (error) {
        console.error("Failed to check existing review:", error);
      } finally {
        setLoading(false);
      }
    };

    if (applicationId) {
      checkExistingReview();
    }
  }, [applicationId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");

    if (rating === 0) {
      setError("Please select a rating");
      return;
    }

    if (!reviewText.trim()) {
      setError("Please write a review");
      return;
    }

    setSubmitting(true);

    try {
      const res = await fetch("/api/reviews/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          applicationId,
          rating,
          reviewText: reviewText.trim(),
        }),
      });

      if (res.ok) {
        onSubmit?.();
      } else {
        const errorData = await res.json();
        setError(errorData.error || "Failed to submit review");
      }
    } catch (error) {
      setError("Failed to submit review. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const getRatingLabel = (rating) => {
    const labels = {
      1: "Poor",
      2: "Fair",
      3: "Good",
      4: "Very Good",
      5: "Excellent"
    };
    return labels[rating] || "";
  };

  if (loading) {
    return (
      <div className={`card ${className}`}>
        <div className="card-content text-center py-8">
          <div className="w-6 h-6 border-2 border-gray-300 border-t-black rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading review...</p>
        </div>
      </div>
    );
  }

  if (existingReview) {
    return (
      <div className={`card ${className}`}>
        <div className="card-header">
          <h2 className="card-title flex items-center gap-2">
            <CheckCircle size={20} className="text-green-600" />
            Review Submitted
          </h2>
          <p className="card-description">
            You have already reviewed this person
          </p>
        </div>

        <div className="card-content">
          <div className="space-y-4">
            {/* Rating Display */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">Your Rating</label>
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    size={24}
                    className={
                      star <= existingReview.rating
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-gray-300"
                    }
                  />
                ))}
              </div>
            </div>

            {/* Review Text Display */}
            <div className="space-y-2">
              <label className="block text-sm font-medium">Your Review</label>
              <div className="p-3 bg-gray-50 rounded-lg border">
                <p className="text-sm">{existingReview.review_text}</p>
              </div>
            </div>

            {/* Submitted Date */}
            <div className="text-xs text-muted-foreground">
              Submitted on {new Date(existingReview.created_at).toLocaleDateString()}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card ${className}`}>
      <div className="card-header">
        <h2 className="card-title flex items-center gap-2">
          <MessageSquare size={20} />
          Leave a Review
        </h2>
        <p className="card-description">
          Share your experience to help others make informed decisions
        </p>
      </div>

      <div className="card-content">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Rating */}
          <div className="space-y-3">
            <label className="block text-sm font-medium">
              Rating <span className="text-destructive">*</span>
            </label>
            <div className="flex flex-col sm:flex-row sm:items-center gap-3">
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    onClick={() => setRating(star)}
                    onMouseEnter={() => setHoveredRating(star)}
                    onMouseLeave={() => setHoveredRating(0)}
                    className="p-1 transition-all hover:scale-110 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded"
                    aria-label={`Rate ${star} star${star > 1 ? 's' : ''}`}
                  >
                    <Star
                      size={28}
                      className={
                        star <= (hoveredRating || rating)
                          ? "fill-yellow-400 text-yellow-400"
                          : "text-muted-foreground hover:text-yellow-300 transition-colors"
                      }
                    />
                  </button>
                ))}
              </div>
              {(rating > 0 || hoveredRating > 0) && (
                <div className="text-sm text-muted-foreground">
                  {getRatingLabel(hoveredRating || rating)}
                </div>
              )}
            </div>
            {rating === 0 && error && (
              <p className="text-destructive text-sm flex items-center gap-1">
                <AlertCircle size={14} />
                Please select a rating
              </p>
            )}
          </div>

          {/* Review Text */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Your Experience <span className="text-destructive">*</span>
            </label>
            <textarea
              value={reviewText}
              onChange={(e) => setReviewText(e.target.value)}
              rows={4}
              className="textarea resize-none"
              placeholder="Share your experience working with this person. What went well? What could be improved? Be honest and constructive..."
              maxLength={500}
              required
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Be honest and constructive in your feedback</span>
              <span>{reviewText.length}/500</span>
            </div>
            {reviewText.trim() === "" && error && (
              <p className="text-destructive text-sm flex items-center gap-1">
                <AlertCircle size={14} />
                Please write a review
              </p>
            )}
          </div>

          {/* Error Message */}
          {error && !error.includes("rating") && !error.includes("review") && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-destructive text-sm flex items-center gap-2">
                <AlertCircle size={16} />
                {error}
              </p>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={submitting || rating === 0 || !reviewText.trim()}
            className="btn-primary w-full flex items-center justify-center gap-2"
          >
            {submitting ? (
              <>
                <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <CheckCircle size={16} />
                Submit Review
              </>
            )}
          </button>
        </form>
      </div>
    </div>
  );
}
