import { withAuth } from '../../../lib/middleware';
import { query } from '../../../lib/db';

export default withAuth(async (req, res) => {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { jobPostId, message, companyName, positionDetails } = req.body;

  // Validate required fields
  if (!jobPostId) {
    return res.status(400).json({ error: 'Job post ID is required' });
  }

  try {
    // Check if already applied
    const existing = await query(
      'SELECT id FROM applications WHERE job_post_id = ? AND referrer_id = ?',
      [jobPostId, req.user.id]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: 'Already applied to this job' });
    }

    // Create application
    const result = await query(
      `INSERT INTO applications (
        job_post_id, referrer_id, message, company_name, position_details, status, created_at
      ) VALUES (?, ?, ?, ?, ?, 'pending', NOW())`,
      [jobPostId, req.user.id, message || '', companyName || '', positionDetails || '']
    );

    // Send notification to job poster
    const jobPosts = await query(
      'SELECT user_id, title FROM job_posts WHERE id = ?',
      [jobPostId]
    );

    if (jobPosts.length > 0) {
      await query(
        `INSERT INTO notifications (
          user_id, type, title, message, related_id, created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())`,
        [
          jobPosts[0].user_id,
          'new_application',
          'New Application',
          `Someone applied to your job post: ${jobPosts[0].title}`,
          result.insertId
        ]
      );
    }

    res.status(201).json({
      id: result.insertId,
      message: 'Application submitted successfully'
    });
  } catch (error) {
    console.error('Failed to create application:', error);
    res.status(500).json({ error: 'Failed to submit application' });
  }
});
