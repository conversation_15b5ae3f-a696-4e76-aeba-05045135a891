import { query } from "../../../lib/db";
import { hashPassword, generateToken } from "../../../lib/auth";
import { serialize } from "cookie";

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { email, password, fullName, phone, userType } = req.body;

  try {
    // Check if user exists
    const existingUsers = await query("SELECT id FROM users WHERE email = ?", [
      email,
    ]);

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: "Email already registered" });
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const result = await query(
      "INSERT INTO users (email, password_hash, user_type, full_name, phone) VALUES (?, ?, ?, ?, ?)",
      [email, passwordHash, userType, fullName, phone]
    );

    const userId = result.insertId;

    // Generate token
    const token = generateToken(userId, userType);

    // Set cookie
    res.setHeader(
      "Set-Cookie",
      serialize("token", token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: "/",
      })
    );

    res.status(201).json({
      user: {
        id: userId,
        email,
        user_type: userType,
        full_name: fullName,
        phone,
      },
      token,
    });
  } catch (error) {
    console.error("Signup error:", error);
    res.status(500).json({ error: "Failed to create account" });
  }
}
