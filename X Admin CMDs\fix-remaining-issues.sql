-- Fix remaining issues: Review access and document permissions
USE u618120801_SplitJob_33;

-- 1. Check current application status for your test applications
SELECT 'Current Status Check:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.status as contract_status,
    c.offer_letter_released
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
WHERE a.id IN (1, 2, 3);

-- 2. Ensure applications are marked as completed for review access
UPDATE applications 
SET status = 'completed'
WHERE id IN (1, 2, 3) 
  AND status != 'completed';

-- 3. Check if profile_documents table exists (for document access)
SELECT 'Checking tables:' as info;
SHOW TABLES LIKE '%document%';

-- 4. Check document table structure
SELECT 'Documents table structure:' as info;
DESCRIBE documents;

-- 5. Check if there are any documents in the documents table
SELECT 'Documents in system:' as info;
SELECT 
    d.id,
    d.user_id,
    d.application_id,
    d.document_type,
    u.full_name as user_name,
    u.user_type
FROM documents d
JOIN users u ON d.user_id = u.id
LIMIT 10;

-- 6. Check if profile_documents table exists
SELECT 'Profile documents table check:' as info;
SELECT COUNT(*) as profile_docs_count
FROM information_schema.tables 
WHERE table_schema = 'u618120801_SplitJob_33' 
  AND table_name = 'profile_documents';

-- 7. If profile_documents doesn't exist, we need to create it or fix the API
-- Let's see what documents exist for our test users
SELECT 'Documents for test applications:' as info;
SELECT 
    a.id as app_id,
    d.user_id,
    d.document_type,
    d.document_url,
    u.full_name,
    u.user_type
FROM applications a
JOIN documents d ON d.application_id = a.id
JOIN users u ON d.user_id = u.id
WHERE a.id IN (1, 2, 3)
ORDER BY a.id, u.user_type, d.document_type;
