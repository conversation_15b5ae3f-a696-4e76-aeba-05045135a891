import { useState, useRef, useEffect } from "react";
import { Upload, File, Check, X, Eye, Trash2, Shield, AlertCircle } from "lucide-react";

export default function ProfileDocuments({ user }) {
  const [uploadingStates, setUploadingStates] = useState({
    aadhar: false,
    pan: false,
    driving_license: false,
    passport: false,
  });
  const [documents, setDocuments] = useState({});
  const [loading, setLoading] = useState(true);
  const fileInputRefs = {
    aadhar: useRef(),
    pan: useRef(),
    driving_license: useRef(),
    passport: useRef(),
  };

  const documentLabels = {
    aadhar: "Aadhar Card",
    pan: "PAN Card", 
    driving_license: "Driving License",
    passport: "Passport",
  };

  const documentDescriptions = {
    aadhar: "Government-issued identity proof",
    pan: "Permanent Account Number card",
    driving_license: "Valid driving license",
    passport: "International travel document",
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      const res = await fetch("/api/profile/documents");
      if (res.ok) {
        const data = await res.json();
        setDocuments(data.documents || {});
      } else {
        console.error("Failed to fetch documents");
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = async (type, file) => {
    if (!file) return;

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      alert("File size must be less than 5MB");
      return;
    }

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/jpg", 
      "application/pdf",
    ];
    if (!allowedTypes.includes(file.type)) {
      alert("Please upload an image (JPG, PNG) or PDF file");
      return;
    }

    setUploadingStates(prev => ({ ...prev, [type]: true }));

    const formData = new FormData();
    formData.append("document", file);
    formData.append("documentType", type);

    try {
      const res = await fetch("/api/profile/documents", {
        method: "POST",
        body: formData,
      });

      if (res.ok) {
        const data = await res.json();
        setDocuments(prev => ({
          ...prev,
          [type]: {
            url: data.url,
            verified: false,
            uploadedAt: new Date().toISOString()
          }
        }));
        alert("Document uploaded successfully!");
      } else {
        const error = await res.json();
        console.error("Upload error:", error);
        alert(error.error || "Failed to upload document");
      }
    } catch (error) {
      console.error("Upload error:", error);
      alert("Failed to upload document: " + error.message);
    } finally {
      setUploadingStates(prev => ({ ...prev, [type]: false }));
    }
  };

  const removeDocument = async (type) => {
    if (!confirm("Are you sure you want to delete this document?")) {
      return;
    }

    try {
      const res = await fetch(`/api/profile/documents?documentType=${type}`, {
        method: "DELETE",
      });

      if (res.ok) {
        setDocuments(prev => {
          const newDocs = { ...prev };
          delete newDocs[type];
          return newDocs;
        });
        
        // Clear file input
        if (fileInputRefs[type].current) {
          fileInputRefs[type].current.value = "";
        }
        
        alert("Document deleted successfully!");
      } else {
        const error = await res.json();
        alert(error.error || "Failed to delete document");
      }
    } catch (error) {
      console.error("Delete error:", error);
      alert("Failed to delete document");
    }
  };

  const viewDocument = (url) => {
    window.open(url, '_blank');
  };

  if (loading) {
    return (
      <div className="card">
        <div className="card-content">
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-500">Loading documents...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h2 className="card-title flex items-center gap-2">
          <Shield size={20} />
          Identity Documents
        </h2>
        <p className="card-description">
          Upload your identity documents for verification. These will be used for contract verification with other users.
        </p>
      </div>

      <div className="card-content space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <AlertCircle className="text-blue-600 flex-shrink-0 mt-0.5" size={16} />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Document Verification Process</p>
              <p>
                Upload your documents here once, and they'll be available for verification in all your contracts. 
                Other users will verify your documents, and you'll verify theirs during the contract process.
              </p>
            </div>
          </div>
        </div>

        {Object.entries(documentLabels).map(([type, label]) => (
          <div key={type} className="border rounded-lg p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-lg mb-1">{label}</h3>
                <p className="text-sm text-gray-500 mb-2">{documentDescriptions[type]}</p>
                
                {documents[type] ? (
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2 text-green-600">
                      <Check size={16} />
                      <span className="text-sm font-medium">Uploaded</span>
                    </div>
                    {documents[type].verified && (
                      <div className="flex items-center gap-1 text-blue-600">
                        <Shield size={14} />
                        <span className="text-xs">Verified</span>
                      </div>
                    )}
                    <span className="text-xs text-gray-400">
                      {new Date(documents[type].uploadedAt).toLocaleDateString()}
                    </span>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">Not uploaded yet</p>
                )}
              </div>

              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                {documents[type] ? (
                  <>
                    <button
                      onClick={() => viewDocument(documents[type].url)}
                      className="btn-outline btn-sm flex items-center justify-center gap-2"
                    >
                      <Eye size={16} />
                      View
                    </button>
                    <button
                      onClick={() => removeDocument(type)}
                      className="btn-outline btn-sm flex items-center justify-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <Trash2 size={16} />
                      Delete
                    </button>
                    <button
                      onClick={() => fileInputRefs[type].current.click()}
                      disabled={uploadingStates[type]}
                      className="btn-primary btn-sm flex items-center justify-center gap-2"
                    >
                      <Upload size={16} />
                      {uploadingStates[type] ? "Uploading..." : "Replace"}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => fileInputRefs[type].current.click()}
                    disabled={uploadingStates[type]}
                    className="btn-primary btn-sm flex items-center justify-center gap-2 w-full sm:w-auto"
                  >
                    <Upload size={16} />
                    {uploadingStates[type] ? "Uploading..." : "Upload"}
                  </button>
                )}

                <input
                  ref={fileInputRefs[type]}
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => handleFileSelect(type, e.target.files[0])}
                  className="hidden"
                />
              </div>
            </div>
          </div>
        ))}

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-6">
          <h4 className="font-medium text-sm mb-2">Supported File Types</h4>
          <p className="text-xs text-gray-600">
            JPG, PNG, PDF • Maximum file size: 5MB • Documents are securely stored and only shared during contract verification
          </p>
        </div>
      </div>
    </div>
  );
}
