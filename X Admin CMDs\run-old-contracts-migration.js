const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Function to load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env.local');
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};

  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      // Remove quotes if present
      envVars[key.trim()] = value.trim().replace(/^["']|["']$/g, '');
    }
  });

  return envVars;
}

async function runOldContractsMigration() {
  let connection;

  try {
    // Load environment variables
    const env = loadEnvFile();

    console.log('🔄 Starting old contracts status migration...');
    console.log('Database configuration:');
    console.log('Host:', env.DB_HOST);
    console.log('User:', env.DB_USER);
    console.log('Database:', env.DB_NAME);

    const isLocalhost = env.DB_HOST === 'localhost' || env.DB_HOST === '127.0.0.1';

    const config = {
      host: env.DB_HOST,
      port: 3306,
      user: env.DB_USER,
      password: env.DB_PASSWORD || '',
      database: env.DB_NAME,
      multipleStatements: true
    };

    // Only add SSL for remote connections
    if (!isLocalhost) {
      config.ssl = {
        rejectUnauthorized: false
      };
    }

    // Create connection
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to database successfully');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'fix-old-contracts-status.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('🔄 Running old contracts migration...');

    // Split the SQL into individual statements
    const statements = migrationSQL.split(';').filter(stmt => stmt.trim().length > 0);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          console.log(`Executing statement ${i + 1}/${statements.length}...`);
          const [results] = await connection.execute(statement);
          
          // If it's a SELECT statement, show the results
          if (statement.toLowerCase().includes('select')) {
            console.log('Results:', results);
          }
        } catch (error) {
          console.error(`Error executing statement ${i + 1}:`, error.message);
          // Continue with other statements
        }
      }
    }

    console.log('✅ Old contracts migration completed successfully!');
    console.log('📊 All existing contracts now have proper status values');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the migration
runOldContractsMigration();
