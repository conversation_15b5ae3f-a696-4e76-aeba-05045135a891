import { withAuth } from "../../../../lib/middleware";
import { query } from "../../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { userId } = req.query;

  if (!userId) {
    return res.status(400).json({ error: "User ID is required" });
  }

  try {
    // Verify that the requesting user has permission to view these documents
    // Check if these users have any contract together (seeker-referrer relationship)
    const contracts = await query(
      `SELECT c.id, a.id as app_id, jp.user_id as seeker_id, a.referrer_id
       FROM contracts c
       JOIN applications a ON c.application_id = a.id
       JOIN job_posts jp ON a.job_post_id = jp.id
       WHERE (
         (jp.user_id = ? AND a.referrer_id = ?) OR
         (jp.user_id = ? AND a.referrer_id = ?)
       )
       AND c.status IN ('signed', 'documents_pending', 'documents_verified', 'offer_released')`,
      [req.user.id, parseInt(userId), parseInt(userId), req.user.id]
    );

    console.log(`Permission check:`, {
      requestingUser: req.user.id,
      targetUser: userId,
      contractsFound: contracts.length,
      contracts: contracts
    });

    if (contracts.length === 0) {
      // For debugging, let's also check what contracts exist
      const allContracts = await query(
        `SELECT c.id, a.id as app_id, jp.user_id as seeker_id, a.referrer_id, c.status
         FROM contracts c
         JOIN applications a ON c.application_id = a.id
         JOIN job_posts jp ON a.job_post_id = jp.id
         WHERE jp.user_id = ? OR a.referrer_id = ?`,
        [req.user.id, req.user.id]
      );

      console.log(`All contracts for user ${req.user.id}:`, allContracts);

      return res.status(403).json({
        error: "Access denied. You can only view documents of users you have active contracts with.",
        debug: {
          requestingUser: req.user.id,
          targetUser: userId,
          userContracts: allContracts.length
        }
      });
    }

    // Get the user's documents from profile_documents table
    let documents = [];
    let userName = null;

    try {
      const profileDocs = await query(
        `SELECT
          pd.document_type,
          pd.document_url,
          pd.is_verified,
          pd.uploaded_at,
          u.full_name as user_name
        FROM profile_documents pd
        JOIN users u ON pd.user_id = u.id
        WHERE pd.user_id = ?
        ORDER BY pd.document_type`,
        [userId]
      );
      documents = profileDocs;
      if (profileDocs.length > 0) {
        userName = profileDocs[0].user_name;
      }
    } catch (error) {
      console.log("Profile documents query failed, trying documents table:", error.message);
    }

    // If no profile documents found, try the documents table
    if (documents.length === 0) {
      try {
        const appDocs = await query(
          `SELECT
            d.document_type,
            d.document_url,
            d.is_verified,
            d.uploaded_at,
            u.full_name as user_name
          FROM documents d
          JOIN users u ON d.user_id = u.id
          WHERE d.user_id = ?
          ORDER BY d.document_type`,
          [userId]
        );
        documents = appDocs;
        if (appDocs.length > 0) {
          userName = appDocs[0].user_name;
        }
      } catch (error) {
        console.log("Documents query also failed:", error.message);
      }
    }

    // If still no documents, get user name separately
    if (!userName) {
      try {
        const users = await query(
          `SELECT full_name FROM users WHERE id = ?`,
          [userId]
        );
        userName = users.length > 0 ? users[0].full_name : "Unknown User";
      } catch (error) {
        userName = "Unknown User";
      }
    }

    // Convert to object format for easier frontend handling
    const documentsObj = {};

    documents.forEach(doc => {
      documentsObj[doc.document_type] = {
        url: doc.document_url,
        verified: doc.is_verified,
        uploadedAt: doc.uploaded_at
      };
    });

    res.status(200).json({
      documents: documentsObj,
      userName: userName
    });

  } catch (error) {
    console.error("Failed to fetch user documents:", error);
    res.status(500).json({ error: "Failed to fetch documents" });
  }
});
