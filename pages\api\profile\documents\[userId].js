import { withAuth } from "../../../../lib/middleware";
import { query } from "../../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { userId } = req.query;

  if (!userId) {
    return res.status(400).json({ error: "User ID is required" });
  }

  try {
    // Verify that the requesting user has permission to view these documents
    // This should only be allowed in the context of an active contract
    const contracts = await query(
      `SELECT c.id
       FROM contracts c
       JOIN applications a ON c.application_id = a.id
       JOIN job_posts jp ON a.job_post_id = jp.id
       WHERE (jp.user_id = ? AND a.referrer_id = ?)
          OR (jp.user_id = ? AND a.referrer_id = ?)
       AND c.status IN ('signed', 'documents_pending', 'documents_verified', 'offer_released')`,
      [req.user.id, userId, userId, req.user.id]
    );

    if (contracts.length === 0) {
      return res.status(403).json({ 
        error: "Access denied. You can only view documents of users you have active contracts with." 
      });
    }

    // Get the user's documents (try profile_documents first, then documents table)
    let documents = await query(
      `SELECT
        pd.document_type,
        pd.document_url,
        pd.is_verified,
        pd.uploaded_at,
        u.full_name as user_name
      FROM profile_documents pd
      JOIN users u ON pd.user_id = u.id
      WHERE pd.user_id = ?
      ORDER BY pd.document_type`,
      [userId]
    ).catch(() => []);

    // If profile_documents table doesn't exist or is empty, try documents table
    if (documents.length === 0) {
      documents = await query(
        `SELECT
          d.document_type,
          d.document_url,
          d.is_verified,
          d.uploaded_at,
          u.full_name as user_name
        FROM documents d
        JOIN users u ON d.user_id = u.id
        WHERE d.user_id = ?
        ORDER BY d.document_type`,
        [userId]
      ).catch(() => []);
    }

    // Convert to object format for easier frontend handling
    const documentsObj = {};
    let userName = null;

    documents.forEach(doc => {
      if (!userName) userName = doc.user_name;
      documentsObj[doc.document_type] = {
        url: doc.document_url,
        verified: doc.is_verified,
        uploadedAt: doc.uploaded_at
      };
    });

    res.status(200).json({ 
      documents: documentsObj,
      userName: userName || "Unknown User"
    });

  } catch (error) {
    console.error("Failed to fetch user documents:", error);
    res.status(500).json({ error: "Failed to fetch documents" });
  }
});
