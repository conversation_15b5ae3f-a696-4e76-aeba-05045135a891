-- Fix empty contract status values
USE u618120801_SplitJob_33;

-- Check current contract status
SELECT 'Current contract status:' as info;
SELECT 
    id,
    application_id,
    status,
    seeker_signed,
    referrer_signed,
    seeker_documents_verified,
    referrer_documents_verified,
    offer_letter_released
FROM contracts 
WHERE id IN (1, 2, 3);

-- Fix contract status based on current state
-- 1. Set offer_released status for contracts where offer letter is released
UPDATE contracts 
SET status = 'offer_released'
WHERE offer_letter_released = 1 
  AND (status = '' OR status IS NULL);

-- 2. Set documents_verified status for contracts where both parties verified documents but no offer released
UPDATE contracts 
SET status = 'documents_verified'
WHERE seeker_documents_verified = 1 
  AND referrer_documents_verified = 1
  AND (offer_letter_released = 0 OR offer_letter_released IS NULL)
  AND (status = '' OR status IS NULL);

-- 3. Set signed status for contracts where both parties signed but documents not verified
UPDATE contracts 
SET status = 'signed'
WHERE seeker_signed = 1 
  AND referrer_signed = 1
  AND (seeker_documents_verified = 0 OR seeker_documents_verified IS NULL 
       OR referrer_documents_verified = 0 OR referrer_documents_verified IS NULL)
  AND (status = '' OR status IS NULL);

-- 4. Set pending_signatures for contracts where only some parties signed
UPDATE contracts 
SET status = 'pending_signatures'
WHERE (seeker_signed = 1 OR referrer_signed = 1)
  AND NOT (seeker_signed = 1 AND referrer_signed = 1)
  AND (status = '' OR status IS NULL);

-- 5. Set default status for any remaining contracts
UPDATE contracts 
SET status = 'pending_signatures'
WHERE status = '' OR status IS NULL;

-- Show results after fix
SELECT 'After status fix:' as info;
SELECT 
    id,
    application_id,
    status,
    seeker_signed,
    referrer_signed,
    seeker_documents_verified,
    referrer_documents_verified,
    offer_letter_released
FROM contracts 
WHERE id IN (1, 2, 3);

-- Test the permission query that was failing
SELECT 'Permission test after fix:' as info;
SELECT c.id, a.id as app_id, jp.user_id as seeker_id, a.referrer_id, c.status
FROM contracts c
JOIN applications a ON c.application_id = a.id
JOIN job_posts jp ON a.job_post_id = jp.id
WHERE (
  (jp.user_id = 3 AND a.referrer_id = 1) OR 
  (jp.user_id = 1 AND a.referrer_id = 3)
)
AND (c.status IN ('signed', 'documents_pending', 'documents_verified', 'offer_released') 
     OR c.status = '' OR c.status IS NULL);
