import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";
import fs from "fs";
import path from "path";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { contractId } = req.body;

  if (!contractId) {
    return res.status(400).json({ error: "Contract ID is required" });
  }

  try {
    // Get contract and application details
    const contracts = await query(
      `
      SELECT 
        c.*,
        a.*,
        jp.title as job_title,
        jp.job_role,
        jp.company_name as job_company,
        jp.location,
        jp.salary_range,
        jp.payment_type,
        jp.payment_percentage,
        jp.payment_fixed,
        seeker.full_name as seeker_name,
        seeker.email as seeker_email,
        referrer.full_name as referrer_name,
        referrer.email as referrer_email
      FROM contracts c
      JOIN applications a ON c.application_id = a.id
      JOIN job_posts jp ON a.job_post_id = jp.id
      JOIN users seeker ON jp.user_id = seeker.id
      JOIN users referrer ON a.referrer_id = referrer.id
      WHERE c.id = ? AND c.status = 'documents_verified'
    `,
      [contractId]
    );

    if (contracts.length === 0) {
      return res.status(404).json({ 
        error: "Contract not found or documents not verified by both parties" 
      });
    }

    const contract = contracts[0];

    // Check if offer letter already exists
    if (contract.offer_letter_released) {
      return res.status(400).json({ 
        error: "Offer letter already released",
        offerLetterUrl: contract.offer_letter_url
      });
    }

    // Generate offer letter PDF
    const pdfBytes = await generateOfferLetterPDF(contract);

    // Save PDF to uploads directory
    const uploadsDir = "./public/uploads";
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const filename = `offer-letter-${contractId}-${Date.now()}.pdf`;
    const filePath = path.join(uploadsDir, filename);
    fs.writeFileSync(filePath, pdfBytes);

    const offerLetterUrl = `/uploads/${filename}`;

    // Update contract with offer letter details
    await query(
      `UPDATE contracts 
       SET offer_letter_released = TRUE, offer_letter_url = ?, offer_letter_released_at = NOW(), status = 'offer_released'
       WHERE id = ?`,
      [offerLetterUrl, contractId]
    );

    // Update application status to completed
    await query(
      'UPDATE applications SET status = "completed" WHERE id = ?',
      [contract.application_id]
    );

    res.status(200).json({
      success: true,
      message: "Offer letter generated and released successfully",
      offerLetterUrl: offerLetterUrl
    });

  } catch (error) {
    console.error("Failed to release offer letter:", error);
    res.status(500).json({ error: "Failed to release offer letter" });
  }
});

async function generateOfferLetterPDF(contract) {
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([600, 800]);
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  const { width, height } = page.getSize();
  let y = height - 50;

  // Header
  page.drawText("OFFER LETTER", {
    x: 250,
    y: y,
    size: 24,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  y -= 60;

  // Content
  const content = [
    `Date: ${new Date().toLocaleDateString()}`,
    "",
    `Dear ${contract.referrer_name},`,
    "",
    "Congratulations! We are pleased to offer you the opportunity to refer",
    `${contract.seeker_name} for the position of ${contract.job_title}`,
    `at ${contract.company_name || contract.job_company || "the company"}.`,
    "",
    "POSITION DETAILS:",
    `• Job Title: ${contract.job_title}`,
    `• Job Role: ${contract.job_role || "As discussed"}`,
    `• Location: ${contract.location || "As per company policy"}`,
    `• Salary Range: ${contract.salary_range || "As per company standards"}`,
    "",
    "REFERRAL COMPENSATION:",
    contract.payment_type === "percentage"
      ? `• ${contract.payment_percentage}% of monthly salary (recurring)`
      : `• Fixed amount: ₹${contract.payment_fixed}`,
    "",
    "TERMS & CONDITIONS:",
    "• Payment will be processed within 30 days of successful placement",
    "• All documents have been verified and approved",
    "• This offer is valid for 30 days from the date of issue",
    "• Both parties have digitally signed the referral agreement",
    "",
    "We appreciate your contribution to our hiring process and look forward",
    "to a successful collaboration.",
    "",
    "Best regards,",
    "SplitJob Team",
    "",
    "---",
    "This is a system-generated document.",
    `Contract ID: ${contract.id}`,
    `Generated on: ${new Date().toLocaleString()}`
  ];

  content.forEach((line) => {
    page.drawText(line, {
      x: 50,
      y: y,
      size: 12,
      font: line.includes("•") || line.includes(":") ? boldFont : font,
      color: rgb(0, 0, 0),
    });
    y -= 18;
  });

  return await pdfDoc.save();
}
