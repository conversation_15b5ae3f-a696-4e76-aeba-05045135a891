const mysql = require("mysql2/promise");
const fs = require('fs');

// Load environment variables manually
const envContent = fs.readFileSync('.env.local', 'utf8');
const envLines = envContent.split('\n');
envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    let value = valueParts.join('=');
    // Remove quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) || 
        (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }
    process.env[key] = value;
  }
});

async function checkUserTable() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('✅ Connected to database');

    // Check table structure
    const [columns] = await connection.execute('DESCRIBE users');
    console.log('\n📋 Users table structure:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''} ${col.Default !== null ? `DEFAULT ${col.Default}` : ''}`);
    });

    // Check admin user
    const [users] = await connection.execute('SELECT * FROM users WHERE email = ?', ['<EMAIL>']);
    if (users.length > 0) {
      console.log('\n👤 Admin user details:');
      console.log('  ID:', users[0].id);
      console.log('  Email:', users[0].email);
      console.log('  User Type:', users[0].user_type);
      console.log('  Full Name:', users[0].full_name);
      console.log('  Phone:', users[0].phone);
      console.log('  Created:', users[0].created_at);
    }

    await connection.end();
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkUserTable();
