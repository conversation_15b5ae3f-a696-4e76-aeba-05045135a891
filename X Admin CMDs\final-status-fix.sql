-- Final fix to ensure all statuses are correct
USE u618120801_SplitJob_33;

-- 1. Ensure contract status is properly set to 'offer_released'
UPDATE contracts 
SET status = 'offer_released'
WHERE offer_letter_released = 1 
  AND status != 'offer_released';

-- 2. Ensure applications are marked as completed
UPDATE applications 
SET status = 'completed'
WHERE id IN (1, 2, 3) 
  AND status != 'completed';

-- 3. Verify the final state
SELECT 'FINAL STATUS CHECK:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.status as contract_status,
    c.offer_letter_released,
    c.offer_letter_released_at,
    'Reviews should be available' as note
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
WHERE a.id IN (1, 2, 3);

-- 4. Check if any reviews already exist
SELECT 'Existing Reviews:' as info;
SELECT 
    r.id,
    r.application_id,
    r.reviewer_id,
    r.reviewed_user_id,
    r.rating,
    reviewer.full_name as reviewer_name,
    reviewed.full_name as reviewed_name
FROM reviews r
JOIN users reviewer ON r.reviewer_id = reviewer.id
JOIN users reviewed ON r.reviewed_user_id = reviewed.id
WHERE r.application_id IN (1, 2, 3);
