import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const posts = await query(
      `
      SELECT 
        jp.*,
        COUNT(DISTINCT a.id) as applications_count,
        COUNT(DISTINCT CASE WHEN a.status = 'completed' THEN a.id END) as completed_count
      FROM job_posts jp
      LEFT JOIN applications a ON jp.id = a.job_post_id
      WHERE jp.user_id = ?
      GROUP BY jp.id
      ORDER BY jp.created_at DESC
    `,
      [req.user.id]
    );

    res.status(200).json({ posts });
  } catch (error) {
    console.error("Failed to fetch user posts:", error);
    res.status(500).json({ error: "Failed to fetch posts" });
  }
});
