-- Simple migration script to add document verification columns
-- You can run this directly in phpMyAdmin or MySQL Workbench

USE u618120801_SplitJob_33;

-- Add seeker_documents_verified column
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS seeker_documents_verified BOOLEAN DEFAULT FALSE;

-- Add referrer_documents_verified column  
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS referrer_documents_verified BOOLEAN DEFAULT FALSE;

-- Add seeker_verified_at column
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS seeker_verified_at TIMESTAMP NULL;

-- Add referrer_verified_at column
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS referrer_verified_at TIMESTAMP NULL;

-- Add offer_letter_released column
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS offer_letter_released BOOLEAN DEFAULT FALSE;

-- Add offer_letter_url column
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS offer_letter_url VARCHAR(500);

-- Add offer_letter_released_at column
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS offer_letter_released_at TIMESTAMP NULL;

-- Show the final structure
DESCRIBE contracts;
