import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import { generateShareableLink } from "../../../lib/utils";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const {
    title,
    description,
    jobRole,
    skills,
    experienceYears,
    desiredCompanies,
    paymentType,
    paymentPercentage,
    paymentFixed,
    visibility,
  } = req.body;

  try {
    const shareableLink =
      visibility === "private" ? generateShareableLink() : null;

    // Debug logging
    console.log('Creating job with payment data:', {
      paymentType,
      paymentPercentage: paymentType === "percentage" ? parseFloat(paymentPercentage) : null,
      paymentFixed: paymentType === "fixed" ? parseFloat(paymentFixed) : null
    });

    console.log('Raw form data received:', {
      paymentType,
      paymentPercentage,
      paymentFixed
    });

    const result = await query(
      `INSERT INTO job_posts (
        user_id, title, description, job_role, skills, experience_years,
        desired_companies, payment_type, payment_percentage, payment_fixed,
        visibility, shareable_link
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        req.user.id,
        title,
        description,
        jobRole,
        skills,
        experienceYears,
        desiredCompanies,
        paymentType,
        paymentType === "percentage" ? parseFloat(paymentPercentage) / 100 : null,
        paymentType === "fixed" ? parseFloat(paymentFixed) : null,
        visibility,
        shareableLink,
      ]
    );

    res.status(201).json({
      id: result.insertId,
      shareable_link: shareableLink,
    });
  } catch (error) {
    console.error("Failed to create job post:", error);
    res.status(500).json({ error: "Failed to create job post" });
  }
});
