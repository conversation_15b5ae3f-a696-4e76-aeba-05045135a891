import { withAuth } from "../../../lib/middleware";
import { generateContractForApplication } from "../../../lib/contractGenerator";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { applicationId } = req.body;

  try {
    let result = await generateContractForApplication(applicationId);

    // If contract already exists, generate PDF for existing contract
    if (!result.success && result.error === "Contract already exists for this application" && result.contractId) {
      // Get existing contract details and generate PDF
      const { query } = require("../../../lib/db");

      const applications = await query(
        `
        SELECT
          a.*,
          jp.title as job_title,
          jp.payment_type,
          jp.payment_percentage,
          jp.payment_fixed,
          seeker.full_name as seeker_name,
          seeker.email as seeker_email,
          referrer.full_name as referrer_name,
          referrer.email as referrer_email
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        JOIN users seeker ON jp.user_id = seeker.id
        JOIN users referrer ON a.referrer_id = referrer.id
        WHERE a.id = ?
      `,
        [applicationId]
      );

      if (applications.length > 0) {
        const { generateContractPDF } = require("../../../lib/contractGenerator");
        const pdfBytes = await generateContractPDF(applications[0]);

        result = {
          success: true,
          pdfBytes: pdfBytes,
          contractId: result.contractId
        };
      }
    }

    if (!result.success) {
      return res.status(404).json({ error: result.error });
    }

    // Return PDF
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="contract-${applicationId}.pdf"`
    );
    res.status(200).send(Buffer.from(result.pdfBytes));
  } catch (error) {
    console.error("Failed to generate contract:", error);
    res.status(500).json({ error: "Failed to generate contract" });
  }
});
