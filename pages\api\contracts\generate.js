import { withAuth } from "../../../lib/middleware";
import { generateContractForApplication } from "../../../lib/contractGenerator";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { applicationId } = req.body;

  try {
    let result = await generateContractForApplication(applicationId);

    // If contract already exists, generate PDF for existing contract
    if (!result.success && result.error === "Contract already exists for this application" && result.contractId) {
      // Get existing contract details and generate PDF
      const { query } = require("../../../lib/db");

      const applications = await query(
        `
        SELECT
          a.*,
          jp.title as job_title,
          jp.payment_type,
          jp.payment_percentage,
          jp.payment_fixed,
          seeker.full_name as seeker_name,
          seeker.email as seeker_email,
          referrer.full_name as referrer_name,
          referrer.email as referrer_email,
          c.seeker_signed,
          c.referrer_signed,
          c.seeker_signed_at,
          c.referrer_signed_at,
          c.seeker_documents_verified,
          c.referrer_documents_verified,
          c.offer_letter_released,
          c.status as contract_status
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        JOIN users seeker ON jp.user_id = seeker.id
        JOIN users referrer ON a.referrer_id = referrer.id
        LEFT JOIN contracts c ON c.application_id = a.id
        WHERE a.id = ?
      `,
        [applicationId]
      );

      if (applications.length > 0) {
        // Import the function using dynamic import for ES6 modules
        const { generateContractPDF } = await import("../../../lib/contractGenerator.js");
        const pdfBytes = await generateContractPDF(applications[0]);

        result = {
          success: true,
          pdfBytes: pdfBytes,
          contractId: result.contractId
        };
      }
    }

    if (!result.success) {
      return res.status(404).json({ error: result.error });
    }

    // Return PDF
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="contract-${applicationId}.pdf"`
    );
    res.status(200).send(Buffer.from(result.pdfBytes));
  } catch (error) {
    console.error("Failed to generate contract:", error);
    res.status(500).json({ error: "Failed to generate contract" });
  }
});
