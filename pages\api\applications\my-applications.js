import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    let applications;

    if (req.user.user_type === "seeker") {
      // Get applications received for seeker's posts
      applications = await query(
        `
        SELECT 
          a.*,
          jp.title as job_title,
          u.full_name as referrer_name,
          u.email as referrer_email
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        JOIN users u ON a.referrer_id = u.id
        WHERE jp.user_id = ?
        ORDER BY a.created_at DESC
      `,
        [req.user.id]
      );
    } else {
      // Get applications made by referrer
      applications = await query(
        `
        SELECT 
          a.*,
          jp.title as job_title,
          u.full_name as seeker_name,
          u.email as seeker_email
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        JOIN users u ON jp.user_id = u.id
        WHERE a.referrer_id = ?
        ORDER BY a.created_at DESC
      `,
        [req.user.id]
      );
    }

    res.status(200).json({ applications });
  } catch (error) {
    console.error("Failed to fetch applications:", error);
    res.status(500).json({ error: "Failed to fetch applications" });
  }
});
