const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Function to load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env.local');
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};

  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      // Remove quotes if present
      envVars[key.trim()] = value.trim().replace(/^["']|["']$/g, '');
    }
  });

  return envVars;
}

async function runMigration() {
  let connection;

  try {
    // Load environment variables
    const env = loadEnvFile();

    console.log('Database configuration:');
    console.log('Host:', env.DB_HOST);
    console.log('User:', env.DB_USER);
    console.log('Database:', env.DB_NAME);
    console.log('Password length:', env.DB_PASSWORD?.length);

    const isLocalhost = env.DB_HOST === 'localhost' || env.DB_HOST === '127.0.0.1';

    const config = {
      host: env.DB_HOST,
      port: 3306,
      user: env.DB_USER,
      password: env.DB_PASSWORD || '',
      database: env.DB_NAME,
      multipleStatements: true
    };

    // Only add SSL for remote connections
    if (!isLocalhost) {
      config.ssl = {
        rejectUnauthorized: false
      };
    }

    // Create connection
    connection = await mysql.createConnection(config);

    console.log('Connected to database successfully');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'add-document-verification-columns.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Running migration...');

    // Execute the migration
    const [results] = await connection.execute(migrationSQL);
    
    console.log('Migration completed successfully!');
    
    // Show the results
    if (Array.isArray(results)) {
      results.forEach((result, index) => {
        if (result && result.length > 0) {
          console.log(`Result ${index + 1}:`, result);
        }
      });
    }

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
runMigration();
