import { useState } from "react";
import { Upload, FileText, AlertCircle, CheckCircle } from "lucide-react";

export default function OfferLetterUpload({ contractId, onSuccess, className = "" }) {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    setError("");
    
    if (!file) {
      setSelectedFile(null);
      return;
    }

    // Validate file type
    if (file.type !== "application/pdf") {
      setError("Please select a PDF file");
      setSelectedFile(null);
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setError("File size must be less than 10MB");
      setSelectedFile(null);
      return;
    }

    setSelectedFile(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError("Please select a file first");
      return;
    }

    setUploading(true);
    setError("");

    try {
      const formData = new FormData();
      formData.append("contractId", contractId);
      formData.append("offerLetter", selectedFile);

      const res = await fetch("/api/contracts/upload-offer-letter", {
        method: "POST",
        body: formData,
      });

      if (res.ok) {
        const data = await res.json();
        onSuccess?.(data);
      } else {
        const error = await res.json();
        setError(error.error || "Failed to upload offer letter");
      }
    } catch (error) {
      console.error("Upload error:", error);
      setError("Failed to upload offer letter");
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className={`card ${className}`}>
      <div className="card-header">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Upload size={20} />
          Upload Offer Letter
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          Upload the official offer letter to share with the referrer
        </p>
      </div>

      <div className="card-content">
        <div className="space-y-4">
          {/* File Upload Area */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Select Offer Letter (PDF only)
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <input
                type="file"
                accept=".pdf,application/pdf"
                onChange={handleFileSelect}
                className="hidden"
                id="offer-letter-upload"
                disabled={uploading}
              />
              <label
                htmlFor="offer-letter-upload"
                className="cursor-pointer flex flex-col items-center gap-2"
              >
                <FileText size={32} className="text-gray-400" />
                <div className="text-sm">
                  <span className="font-medium text-blue-600 hover:text-blue-500">
                    Click to upload
                  </span>
                  <span className="text-gray-500"> or drag and drop</span>
                </div>
                <p className="text-xs text-gray-500">PDF files only, up to 10MB</p>
              </label>
            </div>
          </div>

          {/* Selected File Info */}
          {selectedFile && (
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <FileText size={20} className="text-blue-600" />
              <div className="flex-1">
                <p className="text-sm font-medium text-blue-900">
                  {selectedFile.name}
                </p>
                <p className="text-xs text-blue-600">
                  {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                </p>
              </div>
              <CheckCircle size={16} className="text-green-600" />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg border border-red-200">
              <AlertCircle size={16} className="text-red-600" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {/* Upload Button */}
          <button
            onClick={handleUpload}
            disabled={!selectedFile || uploading}
            className="btn-primary w-full flex items-center justify-center gap-2 disabled:opacity-50"
          >
            {uploading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Uploading...
              </>
            ) : (
              <>
                <Upload size={16} />
                Upload Offer Letter
              </>
            )}
          </button>

          {/* Instructions */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>• The offer letter should be an official document from the company</p>
            <p>• Make sure all details match the job application</p>
            <p>• Once uploaded, the referrer will be able to view the offer letter</p>
            <p>• This action will complete the referral process</p>
          </div>
        </div>
      </div>
    </div>
  );
}
