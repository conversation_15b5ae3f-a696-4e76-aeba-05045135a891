import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { applicationId } = req.query;

  if (!applicationId) {
    return res.status(400).json({ error: "Application ID is required" });
  }

  try {
    // Get application details to determine who is being reviewed
    const applications = await query(
      `
      SELECT 
        a.*,
        jp.user_id as seeker_id
      FROM applications a
      JOIN job_posts jp ON a.job_post_id = jp.id
      WHERE a.id = ? AND a.status = 'completed'
    `,
      [applicationId]
    );

    if (applications.length === 0) {
      return res
        .status(400)
        .json({ error: "Application not found or not completed" });
    }

    const app = applications[0];

    // Determine who is being reviewed
    const reviewedUserId =
      req.user.id === app.seeker_id ? app.referrer_id : app.seeker_id;

    // Check if already reviewed
    const existing = await query(
      `SELECT id, rating, review_text, created_at FROM reviews 
       WHERE application_id = ? AND reviewer_id = ? AND reviewed_user_id = ?`,
      [applicationId, req.user.id, reviewedUserId]
    );

    if (existing.length > 0) {
      return res.status(200).json({ 
        hasReviewed: true, 
        review: existing[0] 
      });
    }

    res.status(200).json({ hasReviewed: false });
  } catch (error) {
    console.error("Failed to check review:", error);
    res.status(500).json({ error: "Failed to check review" });
  }
});
