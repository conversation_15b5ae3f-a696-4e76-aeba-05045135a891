import { withAuth } from '../../../lib/middleware';
import { query } from '../../../lib/db';

export default withAuth(async (req, res) => {
  if (req.method === 'GET') {
    try {
      // Get user profile with ratings
      const users = await query(`
        SELECT
          u.*,
          COUNT(DISTINCT r.id) as total_reviews,
          AVG(r.rating) as average_rating
        FROM users u
        LEFT JOIN reviews r ON u.id = r.reviewed_user_id
        WHERE u.id = ?
        GROUP BY u.id
      `, [req.user.id]);

      if (users.length === 0) {
        return res.status(404).json({ error: 'User not found' });
      }

      const { password_hash, ...userProfile } = users[0];
      res.status(200).json({ user: userProfile });
    } catch (error) {
      console.error('Failed to fetch profile:', error);
      res.status(500).json({ error: 'Failed to fetch profile' });
    }
  } else if (req.method === 'PATCH') {
    const {
      fullName,
      phone,
      bio,
      linkedinUrl,
      githubUrl,
      portfolioUrl,
      resumeUrl
    } = req.body;

    try {
      await query(
        `UPDATE users SET
         full_name = ?, phone = ?, bio = ?,
         linkedin_url = ?, github_url = ?, portfolio_url = ?, resume_url = ?,
         updated_at = NOW()
         WHERE id = ?`,
        [fullName, phone, bio, linkedinUrl, githubUrl, portfolioUrl, resumeUrl, req.user.id]
      );

      res.status(200).json({
        success: true,
        message: 'Profile updated successfully'
      });
    } catch (error) {
      console.error('Failed to update profile:', error);
      res.status(500).json({ error: 'Failed to update profile' });
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
});
