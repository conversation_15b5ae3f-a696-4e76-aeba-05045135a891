import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  const { id } = req.query;

  if (req.method === "PATCH") {
    try {
      const { is_read } = req.body;

      // Verify notification belongs to user
      const notifications = await query(
        "SELECT user_id FROM notifications WHERE id = ?",
        [id]
      );

      if (notifications.length === 0) {
        return res.status(404).json({ error: "Notification not found" });
      }

      if (notifications[0].user_id !== req.user.id) {
        return res.status(403).json({ error: "Access denied" });
      }

      // Update notification
      await query(
        "UPDATE notifications SET is_read = ? WHERE id = ?",
        [is_read, id]
      );

      res.status(200).json({ message: "Notification updated" });
    } catch (error) {
      console.error("Failed to update notification:", error);
      res.status(500).json({ error: "Failed to update notification" });
    }
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
});
