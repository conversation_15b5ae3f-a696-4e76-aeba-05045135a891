import { useState, useRef } from "react";
import { Upload, File, Check, X } from "lucide-react";

export default function DocumentUpload({ applicationId, onUploadComplete }) {
  const [uploading, setUploading] = useState(false);
  const [documents, setDocuments] = useState({
    aadhar: null,
    pan: null,
    driving_license: null,
    passport: null,
  });
  const fileInputRefs = {
    aadhar: useRef(),
    pan: useRef(),
    driving_license: useRef(),
    passport: useRef(),
  };

  const documentLabels = {
    aadhar: "Aadhar Card",
    pan: "PAN Card",
    driving_license: "Driving License",
    passport: "Passport",
  };

  const handleFileSelect = async (type, file) => {
    if (!file) return;

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      alert("File size must be less than 5MB");
      return;
    }

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/jpg",
      "application/pdf",
    ];
    if (!allowedTypes.includes(file.type)) {
      alert("Please upload an image (JPG, PNG) or PDF file");
      return;
    }

    setUploading(true);

    const formData = new FormData();
    formData.append("document", file);
    formData.append("applicationId", applicationId);
    formData.append("documentType", type);

    try {
      const res = await fetch("/api/documents/upload", {
        method: "POST",
        body: formData,
      });

      if (res.ok) {
        const data = await res.json();
        setDocuments((prev) => ({ ...prev, [type]: data.url }));
        alert("Document uploaded successfully!");
        if (onUploadComplete) onUploadComplete();
      } else {
        const error = await res.json();
        console.error("Upload error:", error);
        alert(error.error || "Failed to upload document");
      }
    } catch (error) {
      console.error("Upload error:", error);
      alert("Failed to upload document: " + error.message);
    } finally {
      setUploading(false);
    }
  };

  const removeDocument = (type) => {
    setDocuments((prev) => ({ ...prev, [type]: null }));
    if (fileInputRefs[type].current) {
      fileInputRefs[type].current.value = "";
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">Document Exchange</h2>
      <p className="text-gray-600 mb-6">
        Please upload the required documents for verification. All documents
        will be shared with the other party after both have uploaded.
      </p>

      <div className="space-y-4">
        {Object.entries(documentLabels).map(([type, label]) => (
          <div key={type} className="border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="font-medium mb-1">{label}</h3>
                {documents[type] ? (
                  <div className="flex items-center gap-2 text-green-600">
                    <Check size={16} />
                    <span className="text-sm">Uploaded</span>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">Not uploaded yet</p>
                )}
              </div>

              <div className="flex items-center gap-2">
                {documents[type] ? (
                  <button
                    onClick={() => removeDocument(type)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded transition-colors"
                  >
                    <X size={20} />
                  </button>
                ) : (
                  <>
                    <input
                      ref={fileInputRefs[type]}
                      type="file"
                      accept="image/*,.pdf"
                      onChange={(e) =>
                        handleFileSelect(type, e.target.files[0])
                      }
                      className="hidden"
                    />
                    <button
                      onClick={() => fileInputRefs[type].current.click()}
                      disabled={uploading}
                      className="flex items-center gap-2 px-4 py-2 border rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                    >
                      <Upload size={16} />
                      Upload
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>Note:</strong> Documents will only be accessible to the other
          party after both users have uploaded all required documents.
        </p>
      </div>
    </div>
  );
}
