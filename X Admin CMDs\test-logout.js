// Simple test script to verify logout API works
const fetch = require('node-fetch');

async function testLogout() {
  try {
    console.log('Testing logout API...');
    
    const response = await fetch('http://localhost:3000/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());
    
    const data = await response.json();
    console.log('Response data:', data);
    
    if (response.ok) {
      console.log('✅ Logout API is working correctly!');
    } else {
      console.log('❌ Logout API failed');
    }
  } catch (error) {
    console.error('❌ Error testing logout:', error.message);
  }
}

testLogout();
