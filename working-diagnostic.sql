-- Working diagnostic query for your database
USE u618120801_SplitJob_33;

-- 1. Check current application status for your test applications
SELECT 'Current Status Check:' as info;
SELECT 
    a.id as app_id,
    a.status as app_status,
    c.status as contract_status,
    c.offer_letter_released
FROM applications a
LEFT JOIN contracts c ON c.application_id = a.id
WHERE a.id IN (1, 2, 3);

-- 2. Check if there are documents in the documents table
SELECT 'Documents in documents table:' as info;
SELECT 
    d.id,
    d.user_id,
    d.application_id,
    d.document_type,
    u.full_name as user_name,
    u.user_type
FROM documents d
JOIN users u ON d.user_id = u.id
WHERE d.application_id IN (1, 2, 3)
ORDER BY d.application_id, u.user_type, d.document_type;

-- 3. Check if there are documents in the profile_documents table
SELECT 'Documents in profile_documents table:' as info;
SELECT 
    pd.id,
    pd.user_id,
    pd.document_type,
    u.full_name as user_name,
    u.user_type
FROM profile_documents pd
JOIN users u ON pd.user_id = u.id
LIMIT 10;

-- 4. Check users involved in applications 1, 2, 3
SELECT 'Users in test applications:' as info;
SELECT DISTINCT
    u.id as user_id,
    u.full_name,
    u.user_type,
    'from applications' as source
FROM applications a
JOIN job_posts jp ON a.job_post_id = jp.id
JOIN users u ON (u.id = jp.user_id OR u.id = a.referrer_id)
WHERE a.id IN (1, 2, 3)
ORDER BY u.user_type, u.id;
