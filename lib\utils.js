import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function generateShareableLink() {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}

export function formatCurrency(amount) {
  // Ensure amount is a positive number
  const numAmount = Math.abs(Number(amount) || 0);
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numAmount);
}

export function formatDate(date) {
  return new Intl.DateTimeFormat("en-IN", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(new Date(date));
}

export function formatPercentage(percentage) {
  const num = parseFloat(percentage);
  if (isNaN(num)) return "0";

  // The database stores percentages as decimals (0.10 for 10%, 0.15 for 15%)
  // Convert to percentage by multiplying by 100
  const converted = num * 100;

  // Round to remove floating point precision issues and return as integer if possible
  const rounded = Math.round(converted * 100) / 100;
  return rounded % 1 === 0 ? rounded.toString() : rounded.toString();
}
