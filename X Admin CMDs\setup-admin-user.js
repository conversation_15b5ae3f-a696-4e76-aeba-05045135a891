const mysql = require("mysql2/promise");
const bcrypt = require("bcryptjs");
const fs = require('fs');

// Load environment variables manually
const envContent = fs.readFileSync('.env.local', 'utf8');
const envLines = envContent.split('\n');
envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    let value = valueParts.join('=');
    // Remove quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) || 
        (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }
    process.env[key] = value;
  }
});

async function setupAdminUser() {
  console.log('Setting up admin user...');
  
  try {
    // Connect to database
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('✅ Connected to database');

    // Check if admin user exists
    const [existingUsers] = await connection.execute(
      'SELECT * FROM users WHERE email = ?',
      ['<EMAIL>']
    );

    if (existingUsers.length > 0) {
      console.log('📧 Admin user already exists');
      console.log('Current user data:', {
        id: existingUsers[0].id,
        email: existingUsers[0].email,
        user_type: existingUsers[0].user_type,
        full_name: existingUsers[0].full_name,
        password_hash: existingUsers[0].password_hash.substring(0, 20) + '...'
      });
    }

    // Generate proper password hash for "AdminBro"
    const password = "AdminBro";
    const hashedPassword = await bcrypt.hash(password, 10);
    
    console.log('🔐 Generated password hash for "AdminBro":', hashedPassword);

    if (existingUsers.length > 0) {
      // Update existing user
      await connection.execute(
        'UPDATE users SET password_hash = ?, user_type = ?, full_name = ? WHERE email = ?',
        [hashedPassword, 'admin', 'Admin User', '<EMAIL>']
      );
      console.log('✅ Updated existing admin user');
    } else {
      // Create new admin user
      await connection.execute(
        'INSERT INTO users (email, password_hash, user_type, full_name, phone) VALUES (?, ?, ?, ?, ?)',
        ['<EMAIL>', hashedPassword, 'admin', 'Admin User', '+1234567890']
      );
      console.log('✅ Created new admin user');
    }

    // Verify the user was created/updated correctly
    const [updatedUsers] = await connection.execute(
      'SELECT * FROM users WHERE email = ?',
      ['<EMAIL>']
    );

    if (updatedUsers.length > 0) {
      console.log('✅ Admin user setup complete!');
      console.log('Login credentials:');
      console.log('  Email: <EMAIL>');
      console.log('  Password: AdminBro');
      console.log('  User Type:', updatedUsers[0].user_type);
      
      // Test password verification
      const isValid = await bcrypt.compare(password, updatedUsers[0].password_hash);
      console.log('🔍 Password verification test:', isValid ? '✅ PASS' : '❌ FAIL');
    }

    await connection.end();
    
  } catch (error) {
    console.error('❌ Error setting up admin user:', error);
  }
}

setupAdminUser();
