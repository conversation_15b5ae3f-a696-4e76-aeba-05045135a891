{"name": "split-job", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cookie": "^1.0.2", "formidable": "^3.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.263.1", "mysql2": "^3.6.0", "next": "^15.3.5", "pdf-lib": "^1.17.1", "react": "18.2.0", "react-dom": "18.2.0", "react-signature-canvas": "^1.0.6", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.0"}, "devDependencies": {"autoprefixer": "^10.4.15", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "postcss": "^8.4.29", "tailwindcss": "^3.3.3"}}