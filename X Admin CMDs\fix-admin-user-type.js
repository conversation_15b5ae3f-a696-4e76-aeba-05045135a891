const mysql = require("mysql2/promise");
const bcrypt = require("bcryptjs");
const fs = require('fs');

// Load environment variables manually
const envContent = fs.readFileSync('.env.local', 'utf8');
const envLines = envContent.split('\n');
envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    let value = valueParts.join('=');
    // Remove quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) || 
        (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }
    process.env[key] = value;
  }
});

async function fixAdminUserType() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('✅ Connected to database');

    // Step 1: Modify the user_type enum to include 'admin'
    console.log('🔧 Adding "admin" to user_type enum...');
    await connection.execute(`
      ALTER TABLE users 
      MODIFY COLUMN user_type ENUM('seeker', 'referrer', 'admin') NOT NULL
    `);
    console.log('✅ Updated user_type enum to include admin');

    // Step 2: Update the admin user to have the correct user_type
    console.log('👤 Updating admin user type...');
    await connection.execute(
      'UPDATE users SET user_type = ? WHERE email = ?',
      ['admin', '<EMAIL>']
    );
    console.log('✅ Updated admin user type');

    // Step 3: Verify the changes
    const [users] = await connection.execute('SELECT * FROM users WHERE email = ?', ['<EMAIL>']);
    if (users.length > 0) {
      console.log('\n✅ Admin user setup complete!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: AdminBro');
      console.log('👤 User Type:', users[0].user_type);
      console.log('📛 Full Name:', users[0].full_name);
      
      // Test password verification
      const isValid = await bcrypt.compare('AdminBro', users[0].password_hash);
      console.log('🔍 Password verification:', isValid ? '✅ PASS' : '❌ FAIL');
    }

    // Step 4: Check the updated table structure
    const [columns] = await connection.execute('DESCRIBE users');
    const userTypeColumn = columns.find(col => col.Field === 'user_type');
    console.log('\n📋 Updated user_type column:', userTypeColumn.Type);

    await connection.end();
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixAdminUserType();
