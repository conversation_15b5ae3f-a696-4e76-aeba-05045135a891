import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import formidable from "formidable";
import fs from "fs";
import path from "path";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Parse form data including file upload
  const form = formidable({
    uploadDir: "./public/uploads/signatures",
    keepExtensions: true,
    maxFileSize: 2 * 1024 * 1024, // 2MB limit
    filter: ({ name, originalFilename, mimetype }) => {
      // Only allow image files
      return mimetype && (mimetype.includes("image/png") || mimetype.includes("image/jpeg") || mimetype.includes("image/jpg"));
    },
  });

  // Ensure upload directory exists
  const uploadDir = "./public/uploads/signatures";
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  let contractId, signatureFile;

  try {
    const [fields, files] = await form.parse(req);
    contractId = fields.contractId?.[0];
    signatureFile = files.signature?.[0];

    if (!contractId) {
      return res.status(400).json({ error: "Contract ID is required" });
    }

    if (!signatureFile) {
      return res.status(400).json({ error: "Signature file is required" });
    }

    // Validate file type
    if (!signatureFile.mimetype?.includes("image/")) {
      return res.status(400).json({ error: "Only image files are allowed" });
    }
  } catch (error) {
    console.error("Error parsing form data:", error);
    return res.status(400).json({ error: "Invalid form data" });
  }

  try {
    // Get contract details
    const contracts = await query(
      `SELECT c.*, a.referrer_id, jp.user_id as seeker_id
       FROM contracts c
       JOIN applications a ON c.application_id = a.id
       JOIN job_posts jp ON a.job_post_id = jp.id
       WHERE c.id = ?`,
      [contractId]
    );

    if (contracts.length === 0) {
      return res.status(404).json({ error: "Contract not found" });
    }

    const contract = contracts[0];

    // Determine if user is seeker or referrer
    const isSeeker = req.user.id === contract.seeker_id;
    const isReferrer = req.user.id === contract.referrer_id;

    if (!isSeeker && !isReferrer) {
      return res.status(403).json({ error: "You are not authorized to sign this contract" });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const userType = isSeeker ? "seeker" : "referrer";
    const extension = path.extname(signatureFile.originalFilename || ".png");
    const filename = `signature-${contractId}-${userType}-${timestamp}${extension}`;
    const newPath = path.join(uploadDir, filename);

    // Move file to final location
    fs.renameSync(signatureFile.filepath, newPath);

    // Generate public URL
    const signatureUrl = `/uploads/signatures/${filename}`;

    // Update contract with signature
    const updateField = isSeeker ? "seeker_signature_url" : "referrer_signature_url";
    await query(
      `UPDATE contracts SET ${updateField} = ? WHERE id = ?`,
      [signatureUrl, contractId]
    );

    res.status(200).json({
      success: true,
      message: "Signature uploaded successfully",
      signatureUrl: signatureUrl
    });

  } catch (error) {
    console.error("Failed to upload signature:", error);
    
    // Clean up uploaded file if database update failed
    if (signatureFile?.filepath && fs.existsSync(signatureFile.filepath)) {
      try {
        fs.unlinkSync(signatureFile.filepath);
      } catch (cleanupError) {
        console.error("Failed to cleanup uploaded file:", cleanupError);
      }
    }

    res.status(500).json({ error: "Failed to upload signature" });
  }
});
