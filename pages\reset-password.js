import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Layout from "../components/Layout";
import { Lock, CheckCircle } from "lucide-react";

export default function ResetPassword() {
  const router = useRouter();
  const { token } = router.query;
  const [step, setStep] = useState("request"); // 'request' or 'reset'
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    newPassword: "",
    confirmPassword: "",
  });

  useEffect(() => {
    if (token) {
      setStep("reset");
      verifyToken();
    }
  }, [token]);

  const verifyToken = async () => {
    try {
      const res = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "verify", token }),
      });

      if (!res.ok) {
        setError("Invalid or expired reset link");
      }
    } catch (error) {
      setError("Failed to verify reset link");
    }
  };

  const handleRequestReset = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      const res = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "request",
          email: formData.email,
        }),
      });

      const data = await res.json();

      if (res.ok) {
        setSuccess(true);
      } else {
        setError(data.error || "Failed to request password reset");
      }
    } catch (error) {
      setError("Failed to request password reset");
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (e) => {
    e.preventDefault();
    setError("");

    if (formData.newPassword !== formData.confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (formData.newPassword.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }

    setLoading(true);

    try {
      const res = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "reset",
          token,
          newPassword: formData.newPassword,
        }),
      });

      const data = await res.json();

      if (res.ok) {
        setSuccess(true);
        setTimeout(() => router.push("/login"), 3000);
      } else {
        setError(data.error || "Failed to reset password");
      }
    } catch (error) {
      setError("Failed to reset password");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="max-w-md w-full">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center mb-8">
              <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Lock size={32} />
              </div>
              <h2 className="text-2xl font-bold">
                {step === "request" ? "Reset Password" : "Create New Password"}
              </h2>
              <p className="text-gray-600 mt-2">
                {step === "request"
                  ? "Enter your email to receive a password reset link"
                  : "Enter your new password below"}
              </p>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            {success && step === "request" && (
              <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                <div className="flex items-center gap-2">
                  <CheckCircle size={20} />
                  <div>
                    <p className="font-medium">Check your email!</p>
                    <p className="text-sm">
                      We've sent you a password reset link.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {success && step === "reset" && (
              <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                <div className="flex items-center gap-2">
                  <CheckCircle size={20} />
                  <div>
                    <p className="font-medium">Password reset successfully!</p>
                    <p className="text-sm">Redirecting to login...</p>
                  </div>
                </div>
              </div>
            )}

            {!success && step === "request" && (
              <form onSubmit={handleRequestReset} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    New Password
                  </label>
                  <input
                    type="password"
                    required
                    value={formData.newPassword}
                    onChange={(e) =>
                      setFormData({ ...formData, newPassword: e.target.value })
                    }
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    style={{ backgroundColor: '#F9FAFB' }}
                    placeholder="Enter new password"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    required
                    value={formData.confirmPassword}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        confirmPassword: e.target.value,
                      })
                    }
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    style={{ backgroundColor: '#F9FAFB' }}
                    placeholder="Confirm new password"
                  />
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
                >
                  {loading ? "Resetting..." : "Reset Password"}
                </button>
              </form>
            )}

            <div className="mt-6 text-center">
              <a href="/login" className="text-gray-600 hover:text-gray-800">
                Back to login
              </a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
