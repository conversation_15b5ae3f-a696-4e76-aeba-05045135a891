import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import formidable from "formidable";
import fs from "fs";
import path from "path";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  console.log("Document upload request received");

  try {
    // Create upload directory if it doesn't exist
    const uploadDir = "./public/uploads";
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log("Created upload directory:", uploadDir);
    }

    // Parse the multipart form data manually
    const contentType = req.headers['content-type'];
    if (!contentType || !contentType.includes('multipart/form-data')) {
      return res.status(400).json({ error: "Content type must be multipart/form-data" });
    }

    // For now, let's use a simpler approach with base64 encoding
    // This is a temporary solution - we'll implement proper multipart parsing

    const chunks = [];
    req.on('data', (chunk) => {
      chunks.push(chunk);
    });

    req.on('end', async () => {
      try {
        const buffer = Buffer.concat(chunks);
        const boundary = contentType.split('boundary=')[1];

        if (!boundary) {
          return res.status(400).json({ error: "No boundary found in multipart data" });
        }

        // Parse multipart data
        const parts = buffer.toString('binary').split('--' + boundary);
        let applicationId, documentType, fileData, fileName, mimeType;

        for (const part of parts) {
          if (part.includes('Content-Disposition: form-data; name="applicationId"')) {
            applicationId = part.split('\r\n\r\n')[1].split('\r\n')[0];
          } else if (part.includes('Content-Disposition: form-data; name="documentType"')) {
            documentType = part.split('\r\n\r\n')[1].split('\r\n')[0];
          } else if (part.includes('Content-Disposition: form-data; name="document"')) {
            const headers = part.split('\r\n\r\n')[0];
            const fileContent = part.split('\r\n\r\n')[1];

            // Extract filename
            const filenameMatch = headers.match(/filename="([^"]+)"/);
            fileName = filenameMatch ? filenameMatch[1] : 'unknown';

            // Extract content type
            const contentTypeMatch = headers.match(/Content-Type: ([^\r\n]+)/);
            mimeType = contentTypeMatch ? contentTypeMatch[1] : 'application/octet-stream';

            // Convert binary string back to buffer
            fileData = Buffer.from(fileContent.slice(0, -2), 'binary'); // Remove trailing \r\n
          }
        }

        if (!applicationId || !documentType) {
          return res.status(400).json({ error: "Missing applicationId or documentType" });
        }

        if (!fileData || fileData.length === 0) {
          return res.status(400).json({ error: "No file uploaded" });
        }

        // Validate document type
        const validDocumentTypes = ['aadhar', 'pan', 'driving_license', 'passport'];
        if (!validDocumentTypes.includes(documentType)) {
          return res.status(400).json({ error: "Invalid document type" });
        }

        // Validate file type
        const allowedTypes = [
          "image/jpeg",
          "image/png",
          "image/jpg",
          "application/pdf",
        ];

        if (!allowedTypes.includes(mimeType)) {
          return res.status(400).json({ error: "Invalid file type. Please upload JPG, PNG, or PDF files." });
        }

        console.log("Processing file:", {
          fileName,
          size: fileData.length,
          mimeType,
          applicationId,
          documentType
        });

        // Generate unique filename
        const ext = path.extname(fileName || '');
        const uniqueFilename = `${Date.now()}-${Math.random()
          .toString(36)
          .substring(7)}${ext}`;
        const filePath = path.join(uploadDir, uniqueFilename);

        // Save file to disk
        fs.writeFileSync(filePath, fileData);
        console.log("File saved to:", filePath);

        // Save to database
        await query(
          `INSERT INTO documents (
            user_id, application_id, document_type, document_url
          ) VALUES (?, ?, ?, ?)`,
          [req.user.id, applicationId, documentType, `/uploads/${uniqueFilename}`]
        );

        console.log("Document saved to database");

        res.status(200).json({
          url: `/uploads/${uniqueFilename}`,
          message: "Document uploaded successfully"
        });

      } catch (error) {
        console.error("Failed to process upload:", error);
        res.status(500).json({
          error: "Failed to process upload",
          details: error.message
        });
      }
    });

  } catch (error) {
    console.error("Failed to save document:", error);
    res.status(500).json({
      error: "Failed to save document",
      details: error.message
    });
  }
});
