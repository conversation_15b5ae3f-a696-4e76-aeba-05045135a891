import { getUserFromToken } from "./auth";

export function withAuth(handler) {
  return async (req, res) => {
    const token =
      req.cookies?.token || req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({ error: "Authentication required" });
    }

    const user = await getUserFromToken(token);
    if (!user) {
      return res.status(401).json({ error: "Invalid token" });
    }

    req.user = user;
    return handler(req, res);
  };
}

export function withUserType(userType) {
  return (handler) => {
    return withAuth(async (req, res) => {
      if (req.user.user_type !== userType) {
        return res.status(403).json({ error: "Access denied" });
      }
      return handler(req, res);
    });
  };
}
