-- Database migration to add document verification columns
-- Run this in phpMyAdmin for database: u618120801_SplitJob_33

USE u618120801_SplitJob_33;

-- Add seeker_documents_verified column
ALTER TABLE contracts ADD COLUMN seeker_documents_verified BOOLEAN DEFAULT FALSE;

-- Add referrer_documents_verified column  
ALTER TABLE contracts ADD COLUMN referrer_documents_verified BOOLEAN DEFAULT FALSE;

-- Add seeker_verified_at column
ALTER TABLE contracts ADD COLUMN seeker_verified_at TIMESTAMP NULL;

-- Add referrer_verified_at column
ALTER TABLE contracts ADD COLUMN referrer_verified_at TIMESTAMP NULL;

-- Add offer_letter_released column
ALTER TABLE contracts ADD COLUMN offer_letter_released BOOLEAN DEFAULT FALSE;

-- Add offer_letter_url column
ALTER TABLE contracts ADD COLUMN offer_letter_url VARCHAR(500);

-- Add offer_letter_released_at column
ALTER TABLE contracts ADD COLUMN offer_letter_released_at TIMESTAMP NULL;

-- Verify the changes
DESCRIBE contracts;