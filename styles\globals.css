@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        /* NextJS-inspired black and white theme */
        --background: 0 0% 100%;
        --foreground: 0 0% 0%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 0%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 0%;
        --primary: 0 0% 0%;
        --primary-foreground: 0 0% 100%;
        --secondary: 0 0% 96%;
        --secondary-foreground: 0 0% 0%;
        --muted: 0 0% 96%;
        --muted-foreground: 0 0% 40%;
        --accent: 0 0% 96%;
        --accent-foreground: 0 0% 0%;
        --destructive: 0 84% 60%;
        --destructive-foreground: 0 0% 100%;
        --border: 0 0% 90%;
        --input: 0 0% 90%;
        --ring: 0 0% 0%;
        --radius: 0.5rem;

        /* Additional NextJS-style variables */
        --gray-50: 0 0% 98%;
        --gray-100: 0 0% 96%;
        --gray-200: 0 0% 90%;
        --gray-300: 0 0% 83%;
        --gray-400: 0 0% 64%;
        --gray-500: 0 0% 45%;
        --gray-600: 0 0% 32%;
        --gray-700: 0 0% 25%;
        --gray-800: 0 0% 15%;
        --gray-900: 0 0% 9%;
        --gray-950: 0 0% 4%;
    }

    .dark {
        --background: 0 0% 0%;
        --foreground: 0 0% 100%;
        --card: 0 0% 4%;
        --card-foreground: 0 0% 100%;
        --popover: 0 0% 4%;
        --popover-foreground: 0 0% 100%;
        --primary: 0 0% 100%;
        --primary-foreground: 0 0% 0%;
        --secondary: 0 0% 9%;
        --secondary-foreground: 0 0% 100%;
        --muted: 0 0% 9%;
        --muted-foreground: 0 0% 64%;
        --accent: 0 0% 9%;
        --accent-foreground: 0 0% 100%;
        --destructive: 0 62% 30%;
        --destructive-foreground: 0 0% 100%;
        --border: 0 0% 15%;
        --input: 0 0% 15%;
        --ring: 0 0% 100%;
    }

    .dark .input,
    .dark .textarea,
    .dark .select {
        background-color: #F9FAFB;
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground antialiased;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        font-feature-settings: 'cv11', 'ss01';
        font-variation-settings: 'opsz' 32;
        scroll-behavior: smooth;
    }

    h1, h2, h3, h4, h5, h6 {
        @apply font-semibold tracking-tight;
    }

    h1 {
        @apply text-4xl lg:text-5xl;
    }

    h2 {
        @apply text-3xl lg:text-4xl;
    }

    h3 {
        @apply text-2xl lg:text-3xl;
    }

    h4 {
        @apply text-xl lg:text-2xl;
    }

    h5 {
        @apply text-lg lg:text-xl;
    }

    h6 {
        @apply text-base lg:text-lg;
    }

    p {
        @apply leading-7 text-muted-foreground;
    }

    a {
        @apply text-foreground hover:text-muted-foreground transition-colors;
    }

    code {
        @apply relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold;
    }

    pre {
        @apply overflow-x-auto rounded-lg bg-muted p-4;
    }

    blockquote {
        @apply border-l-2 border-muted-foreground pl-6 italic;
    }
}

@layer components {
    /* Button Components */
    .btn {
        @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
    }

    .btn-primary {
        @apply btn bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4;
    }

    .btn-secondary {
        @apply btn bg-secondary text-secondary-foreground hover:bg-secondary/80 h-10 py-2 px-4;
    }

    .btn-outline {
        @apply btn border border-input hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4;
    }

    .btn-ghost {
        @apply btn hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4;
    }

    .btn-sm {
        @apply h-9 px-3 text-xs;
    }

    .btn-lg {
        @apply h-11 px-8;
    }

    /* Card Components */
    .card {
        @apply rounded-lg border bg-card text-card-foreground shadow-sm;
    }

    .card-header {
        @apply flex flex-col space-y-1.5 p-6;
    }

    .card-title {
        @apply text-2xl font-semibold leading-none tracking-tight;
    }

    .card-description {
        @apply text-sm text-muted-foreground;
    }

    .card-content {
        @apply p-6 pt-0;
    }

    .card-footer {
        @apply flex items-center p-6 pt-0;
    }

    .card-hover {
        @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 cursor-pointer;
    }

    /* Input Components */
    .input {
        @apply flex h-10 w-full rounded-md border border-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
        background-color: #F9FAFB;
    }

    .textarea {
        @apply flex min-h-[80px] w-full rounded-md border border-input px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
        background-color: #F9FAFB;
    }

    .select {
        @apply flex h-10 w-full items-center justify-between rounded-md border border-input px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
        background-color: #F9FAFB;
    }

    /* Badge Components */
    .badge {
        @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    }

    .badge-default {
        @apply badge border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
    }

    .badge-secondary {
        @apply badge border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
    }

    .badge-outline {
        @apply badge text-foreground;
    }

    /* Status badges */
    .status-pending {
        @apply badge bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800;
    }

    .status-accepted {
        @apply badge bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800;
    }

    .status-rejected {
        @apply badge bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800;
    }

    .status-in-progress {
        @apply badge bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800;
    }

    .status-completed {
        @apply badge bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800;
    }

    /* Navigation */
    .nav-link {
        @apply text-sm font-medium transition-colors hover:text-primary;
    }

    .nav-link-active {
        @apply nav-link text-primary;
    }

    /* Layout */
    .container {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .section {
        @apply py-12 lg:py-16;
    }

    .section-sm {
        @apply py-8 lg:py-12;
    }

    .section-lg {
        @apply py-16 lg:py-24;
    }
}

@layer utilities {
    /* Custom utility classes */
    .gradient-text {
        @apply bg-gradient-to-r from-black to-gray-600 bg-clip-text text-transparent dark:from-white dark:to-gray-400;
    }

    .text-balance {
        text-wrap: balance;
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Mobile-specific utilities */
    .touch-manipulation {
        touch-action: manipulation;
    }

    .safe-area-inset-top {
        padding-top: env(safe-area-inset-top);
    }

    .safe-area-inset-bottom {
        padding-bottom: env(safe-area-inset-bottom);
    }

    .safe-area-inset-left {
        padding-left: env(safe-area-inset-left);
    }

    .safe-area-inset-right {
        padding-right: env(safe-area-inset-right);
    }

    /* Responsive text utilities */
    .text-responsive-xs {
        @apply text-xs sm:text-sm;
    }

    .text-responsive-sm {
        @apply text-sm sm:text-base;
    }

    .text-responsive-base {
        @apply text-base sm:text-lg;
    }

    .text-responsive-lg {
        @apply text-lg sm:text-xl;
    }

    .text-responsive-xl {
        @apply text-xl sm:text-2xl;
    }

    .text-responsive-2xl {
        @apply text-2xl sm:text-3xl;
    }

    /* Responsive spacing utilities */
    .space-responsive {
        @apply space-y-4 sm:space-y-6;
    }

    .gap-responsive {
        @apply gap-4 sm:gap-6;
    }

    .p-responsive {
        @apply p-4 sm:p-6;
    }

    .px-responsive {
        @apply px-4 sm:px-6;
    }

    .py-responsive {
        @apply py-4 sm:py-6;
    }

    /* Grid utilities */
    .grid-responsive-1 {
        @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
    }

    .grid-responsive-2 {
        @apply grid-cols-1 md:grid-cols-2;
    }

    .grid-responsive-3 {
        @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
    }

    .grid-responsive-4 {
        @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
    }

    /* Line clamping */
    .line-clamp-1 {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Animation utilities */
    .animate-in {
        animation: animate-in 0.5s ease-out;
    }

    .animate-out {
        animation: animate-out 0.5s ease-in;
    }

    .fade-in {
        animation: fade-in 0.3s ease-out;
    }

    .slide-up {
        animation: slide-up 0.3s ease-out;
    }

    .slide-down {
        animation: slide-down 0.3s ease-out;
    }
}

@keyframes animate-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes animate-out {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-down {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile-first responsive design helpers */
@media (max-width: 640px) {
    .container {
        @apply px-4;
    }

    .card {
        @apply mx-0;
    }

    .card-content {
        @apply p-4;
    }

    .card-header {
        @apply p-4 pb-0;
    }

    .card-footer {
        @apply p-4 pt-0;
    }

    .btn {
        @apply w-full sm:w-auto min-h-[44px];
    }

    .section {
        @apply py-8;
    }

    /* Mobile navigation improvements */
    .nav-link {
        @apply py-3 px-4 block;
    }

    /* Form improvements for mobile */
    .input, .textarea, .select {
        @apply text-base; /* Prevents zoom on iOS */
    }

    /* Touch targets */
    button, .btn, [role="button"] {
        @apply min-h-[44px] min-w-[44px];
    }
}

/* Tablet optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
    .container {
        @apply px-6;
    }

    .card {
        @apply mx-0;
    }

    .grid-cols-auto-fit {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
    .container {
        @apply px-8;
    }

    .grid-cols-auto-fit {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card {
        @apply shadow-sm;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .animate-spin {
        animation: none;
    }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
    :root {
        color-scheme: dark;
    }

    .input, .textarea, .select {
        background-color: #F9FAFB;
    }
}

/* Landscape mobile optimizations */
@media (max-height: 500px) and (orientation: landscape) {
    .section {
        @apply py-4;
    }

    .card-header {
        @apply p-4 pb-2;
    }

    .card-content {
        @apply p-4 pt-2;
    }
}

/* Focus styles for accessibility */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Ensure all input elements have the correct background color */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="search"],
textarea,
select {
    background-color: #F9FAFB !important;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }
}
