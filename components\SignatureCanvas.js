import { useRef, useState, useEffect } from "react";
import { Pen, RotateCcw, Check, Upload } from "lucide-react";

export default function SignatureCanvas({ onSignature, contractId, className = "" }) {
  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    ctx.strokeStyle = "#000000";
    ctx.lineWidth = 2;
    ctx.lineCap = "round";
    ctx.lineJoin = "round";

    // Set canvas background to white
    ctx.fillStyle = "#ffffff";
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }, []);

  const startDrawing = (e) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext("2d");
    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  const draw = (e) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext("2d");
    ctx.lineTo(x, y);
    ctx.stroke();
    setHasSignature(true);
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    ctx.fillStyle = "#ffffff";
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    setHasSignature(false);
    setError("");
  };

  const saveSignature = async () => {
    if (!hasSignature) {
      setError("Please draw your signature first");
      return;
    }

    setUploading(true);
    setError("");

    try {
      const canvas = canvasRef.current;
      
      // Convert canvas to blob
      canvas.toBlob(async (blob) => {
        const formData = new FormData();
        formData.append("contractId", contractId);
        formData.append("signature", blob, "signature.png");

        try {
          const res = await fetch("/api/contracts/upload-signature", {
            method: "POST",
            body: formData,
          });

          if (res.ok) {
            const data = await res.json();
            onSignature?.(data);
          } else {
            const error = await res.json();
            setError(error.error || "Failed to save signature");
          }
        } catch (error) {
          console.error("Upload error:", error);
          setError("Failed to save signature");
        } finally {
          setUploading(false);
        }
      }, "image/png");
    } catch (error) {
      console.error("Signature save error:", error);
      setError("Failed to save signature");
      setUploading(false);
    }
  };

  return (
    <div className={`card ${className}`}>
      <div className="card-header">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Pen size={20} />
          Digital Signature
        </h3>
        <p className="text-sm text-muted-foreground mt-1">
          Draw your signature in the box below
        </p>
      </div>

      <div className="card-content">
        <div className="space-y-4">
          {/* Canvas */}
          <div className="border-2 border-gray-300 rounded-lg p-4 bg-white">
            <canvas
              ref={canvasRef}
              width={400}
              height={150}
              className="border border-gray-200 rounded cursor-crosshair w-full"
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={stopDrawing}
              onMouseLeave={stopDrawing}
              style={{ touchAction: "none" }}
            />
          </div>

          {/* Instructions */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>• Use your mouse or touchpad to draw your signature</p>
            <p>• Make sure your signature is clear and legible</p>
            <p>• You can clear and redraw if needed</p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg border border-red-200">
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={clearSignature}
              disabled={uploading}
              className="btn-outline btn-sm flex items-center gap-2 disabled:opacity-50"
            >
              <RotateCcw size={16} />
              Clear
            </button>
            <button
              onClick={saveSignature}
              disabled={!hasSignature || uploading}
              className="btn-primary btn-sm flex items-center gap-2 disabled:opacity-50 flex-1"
            >
              {uploading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Check size={16} />
                  Save Signature
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
